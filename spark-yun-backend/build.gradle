dependencies {

    api(project(':spark-yun-backend:spark-yun-modules'))
    api(project(':spark-yun-vip:spark-yun-license'))

    // quartz
    implementation 'org.springframework.boot:spring-boot-starter-quartz'
    implementation 'com.mchange:c3p0:*******'

    // JsqlParser
    implementation 'com.github.jsqlparser:jsqlparser:4.6'

    // lucene
    implementation 'org.apache.lucene:lucene-core:8.11.2'
    implementation 'org.apache.lucene:lucene-queryparser:8.11.2'
    implementation 'org.apache.lucene:lucene-highlighter:8.11.2'
    implementation 'org.apache.lucene:lucene-analyzers:3.6.2'
    implementation 'org.apache.lucene:lucene-analyzers-common:8.11.2'

    // truelicense
    implementation 'global.namespace.truelicense:truelicense-v4:4.0.3'
}
