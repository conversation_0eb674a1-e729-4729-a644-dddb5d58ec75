package com.isxcode.star.vip.modules.view.data;

import com.alibaba.fastjson2.JSON;
import com.isxcode.star.api.datasource.constants.DatasourceType;
import com.isxcode.star.api.view.dto.DataSql;
import com.isxcode.star.api.view.dto.EchartOption;
import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.backend.api.base.exceptions.IsxViewException;
import com.isxcode.star.modules.datasource.service.DatasourceService;
import com.isxcode.star.modules.view.entity.ViewCardEntity;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
public abstract class ViewDataExecutor {

    private final DatasourceService datasourceService;

    protected abstract EchartOption getData(ViewCardEntity viewCardEntity, DataSql dataSql);

    protected void checkSql(DataSql dataSql) {

        // 检查每一个sql是否合法
        dataSql.getSqlList().forEach(sql -> {
            try {
                datasourceService.checkSqlValid(sql);
            } catch (IsxAppException e) {
                throw new IsxViewException("聚合sql异常，请检查sql:" + e.getMsg());
            }
        });
    }

    public EchartOption getViewData(ViewCardEntity viewCardEntity, DataSql dataSql) {

        if (dataSql == null || dataSql.getSqlList() == null) {
            return JSON.parseObject(viewCardEntity.getExampleData(), EchartOption.class);
        }

        // 把sql中的空行全部删除
        List<String> sqlList = dataSql.getSqlList().stream().filter(Strings::isNotBlank).collect(Collectors.toList());

        // 判断sql是否为空
        if (sqlList.isEmpty()) {
            return JSON.parseObject(viewCardEntity.getExampleData(), EchartOption.class);
        }

        dataSql.setSqlList(sqlList);
        try {
            if (!DatasourceType.DM
                .equals(datasourceService.getDatasource(viewCardEntity.getDatasourceId()).getDbType())) {
                checkSql(dataSql);
            }
        } catch (IsxViewException e) {
            throw new IsxAppException(e.getMsg());
        }

        return getData(viewCardEntity, dataSql);
    }

    public abstract String getViewType();
}
