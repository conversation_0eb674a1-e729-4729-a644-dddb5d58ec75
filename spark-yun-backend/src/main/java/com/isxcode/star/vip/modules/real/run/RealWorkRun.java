package com.isxcode.star.vip.modules.real.run;

import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSON;
import com.isxcode.star.api.agent.constants.AgentUrl;
import com.isxcode.star.api.agent.req.*;
import com.isxcode.star.api.agent.res.GetWorkStderrLogRes;
import com.isxcode.star.api.api.constants.PathConstants;
import com.isxcode.star.api.cluster.constants.ClusterNodeStatus;
import com.isxcode.star.api.cluster.dto.ScpFileEngineNodeDto;
import com.isxcode.star.api.datasource.constants.DatasourceType;
import com.isxcode.star.api.datasource.dto.KafkaConfig;
import com.isxcode.star.api.real.res.RunRealWorkRes;
import com.isxcode.star.api.work.constants.WorkLog;
import com.isxcode.star.backend.api.base.exceptions.WorkRunException;
import com.isxcode.star.api.work.dto.DatasourceConfig;
import com.isxcode.star.api.work.dto.SyncWorkConfig;
import com.isxcode.star.api.work.res.RunWorkRes;
import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.backend.api.base.pojos.BaseResponse;
import com.isxcode.star.backend.api.base.properties.IsxAppProperties;
import com.isxcode.star.common.utils.aes.AesUtils;
import com.isxcode.star.common.utils.http.HttpUtils;
import com.isxcode.star.common.utils.path.PathUtils;
import com.isxcode.star.modules.cluster.entity.ClusterEntity;
import com.isxcode.star.modules.cluster.entity.ClusterNodeEntity;
import com.isxcode.star.modules.cluster.mapper.ClusterNodeMapper;
import com.isxcode.star.modules.cluster.repository.ClusterNodeRepository;
import com.isxcode.star.modules.cluster.repository.ClusterRepository;
import com.isxcode.star.modules.datasource.entity.DatasourceEntity;
import com.isxcode.star.modules.datasource.service.DatasourceService;
import com.isxcode.star.modules.datasource.source.DataSourceFactory;
import com.isxcode.star.modules.datasource.source.Datasource;
import com.isxcode.star.modules.file.entity.FileEntity;
import com.isxcode.star.modules.file.repository.FileRepository;
import com.isxcode.star.modules.func.entity.FuncEntity;
import com.isxcode.star.modules.func.mapper.FuncMapper;
import com.isxcode.star.modules.func.repository.FuncRepository;
import com.isxcode.star.modules.real.entity.RealEntity;
import com.isxcode.star.modules.real.repository.RealRepository;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

import static com.isxcode.star.common.utils.ssh.SshUtils.scpJar;

@Slf4j
@Service
@RequiredArgsConstructor
public class RealWorkRun {

    private final ClusterRepository clusterRepository;

    private final ClusterNodeRepository clusterNodeRepository;

    private final IsxAppProperties isxAppProperties;

    private final DatasourceService datasourceService;

    private final RealRepository realRepository;

    private final AesUtils aesUtils;

    private final FuncRepository funcRepository;

    private final ClusterNodeMapper clusterNodeMapper;

    private final FuncMapper funcMapper;

    private final FileRepository fileRepository;

    private final DataSourceFactory dataSourceFactory;

    public RealEntity updateRealSubmitLog(RealEntity container, StringBuilder logBuilder) {

        container.setSubmitLog(logBuilder.toString());
        return realRepository.saveAndFlush(container);
    }

    public RunRealWorkRes runRealWork(RealEntity realWork) {

        // 构建提交日志
        StringBuilder submitLog = new StringBuilder();

        // 检查计算集群是否存在
        Optional<ClusterEntity> calculateEngineEntityOptional = clusterRepository.findById(realWork.getClusterId());
        if (!calculateEngineEntityOptional.isPresent()) {
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "申请资源失败 : 计算引擎不存在  \n");
        }

        // 检测集群中是否有启动节点
        List<ClusterNodeEntity> allEngineNodes = clusterNodeRepository
            .findAllByClusterIdAndStatus(calculateEngineEntityOptional.get().getId(), ClusterNodeStatus.RUNNING);
        if (allEngineNodes.isEmpty()) {
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "申请资源失败 : 集群不存在可用节点，请切换一个集群  \n");
        }

        // 节点选择随机数
        ClusterNodeEntity engineNode = allEngineNodes.get(new Random().nextInt(allEngineNodes.size()));
        submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("申请资源完成，激活节点:【")
            .append(engineNode.getName()).append("】\n");
        realWork = updateRealSubmitLog(realWork, submitLog);

        // 判断用户资源配置方式
        Map<String, String> sparkConfig =
            JSON.parseObject(realWork.getSparkConfig(), new TypeReference<Map<String, String>>() {}.getType());

        // 检测kafka数据源
        SyncWorkConfig syncWorkConfig = JSON.parseObject(realWork.getSyncConfig(), SyncWorkConfig.class);
        if (syncWorkConfig == null) {
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "请检查实时作业配置  \n");
        }

        // 判断是否配置了映射关系
        if (syncWorkConfig.getColumnMap().isEmpty()) {
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "检查作业失败 : 请配置字段映射关系  \n");
        }

        // 开始构建作业
        submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("开始构建作业\n");

        // 封装去向Datasource的信息
        DatasourceEntity targetDatasource = datasourceService.getDatasource(syncWorkConfig.getTargetDBId());
        DatasourceConfig targetConfig = DatasourceConfig.builder().dbTable(syncWorkConfig.getTargetTable())
            .driver(datasourceService.getDriverClass(targetDatasource.getDbType())).url(targetDatasource.getJdbcUrl())
            .user(targetDatasource.getUsername()).password(aesUtils.decrypt(targetDatasource.getPasswd())).build();
        syncWorkConfig.setTargetDatabase(targetConfig);

        // 封装kafka的信息
        KafkaConfig dbKafkaConfig;
        if (DatasourceType.KAFKA.equals(syncWorkConfig.getSourceDBType())) {
            DatasourceEntity kafkaDatasource = datasourceService.getDatasource(syncWorkConfig.getSourceDBId());
            dbKafkaConfig = JSON.parseObject(kafkaDatasource.getKafkaConfig(), KafkaConfig.class);
        } else {
            DatasourceEntity kafkaDatasource = datasourceService.getDatasource(syncWorkConfig.getKafkaSourceId());
            dbKafkaConfig = JSON.parseObject(kafkaDatasource.getKafkaConfig(), KafkaConfig.class);
        }
        dbKafkaConfig.setProperties(dbKafkaConfig.getProperties());
        dbKafkaConfig.setStartingOffsets("latest");
        dbKafkaConfig.setDurationTime("1s");
        dbKafkaConfig.setGroupIdPrefix("test-consumer-group");
        syncWorkConfig.setKafkaConfig(dbKafkaConfig);

        // 开始构造SparkSubmit
        SparkSubmit sparkSubmit =
            SparkSubmit.builder().verbose(true).mainClass("com.isxcode.star.plugin.real.sync.Execute")
                .appResource("spark-real-sync-plugin.jar").conf(genSparkSubmitConfig(sparkConfig)).build();

        // 开始构造PluginReq
        PluginReq pluginReq = PluginReq.builder().syncWorkConfig(syncWorkConfig).sparkConfig(sparkConfig).build();

        // 开始构造deployContainerReq
        SubmitWorkReq executeReq = new SubmitWorkReq();
        executeReq.setWorkId(realWork.getId());
        executeReq.setWorkType("REAL_WORK");
        executeReq.setWorkInstanceId(realWork.getId());

        executeReq.setSparkSubmit(sparkSubmit);
        executeReq.setPluginReq(pluginReq);
        executeReq.setAgentHomePath(engineNode.getAgentHomePath() + "/" + PathConstants.AGENT_PATH_NAME);
        executeReq.setClusterType(calculateEngineEntityOptional.get().getClusterType());
        executeReq.setSparkHomePath(engineNode.getSparkHomePath());

        // 解析db
        if (DatasourceType.HIVE.equals(targetDatasource.getDbType())) {
            try {
                Datasource datasourceTmp = dataSourceFactory.getDatasource(DatasourceType.HIVE);
                String database = datasourceTmp.parseDbName(targetDatasource.getJdbcUrl());
                if (!Strings.isEmpty(database)) {
                    pluginReq.setDatabase(database);
                }
            } catch (IsxAppException e) {
                throw new WorkRunException(LocalDateTime.now() + WorkLog.ERROR_INFO + e.getMsg() + "\n");
            }
            // 如果数据库id不为空,则替换hive的metastore url
            pluginReq.getSparkConfig().put("hive.metastore.uris", targetDatasource.getMetastoreUris());
        }

        // 导入自定义函数
        ScpFileEngineNodeDto scpFileEngineNodeDto =
            clusterNodeMapper.engineNodeEntityToScpFileEngineNodeDto(engineNode);
        scpFileEngineNodeDto.setPasswd(aesUtils.decrypt(scpFileEngineNodeDto.getPasswd()));
        String fileDir = PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "file"
            + File.separator + engineNode.getTenantId();
        if (realWork.getFuncConfig() != null) {
            List<FuncEntity> allFunc =
                funcRepository.findAllById(JSON.parseArray(realWork.getFuncConfig(), String.class));
            allFunc.forEach(e -> {
                try {
                    scpJar(scpFileEngineNodeDto, fileDir + File.separator + e.getFileId(),
                        engineNode.getAgentHomePath() + "/zhiqingyun-agent/file/" + e.getFileId() + ".jar");
                } catch (JSchException | SftpException | InterruptedException | IOException ex) {
                    log.error(ex.getMessage(), ex);
                    throw new IsxAppException(
                        LocalDateTime.now() + WorkLog.ERROR_INFO + "自定义函数jar文件上传失败，请检查文件是否上传或者重新上传\n");
                }
            });
            pluginReq.setFuncInfoList(funcMapper.funcEntityListToFuncInfoList(allFunc));
            executeReq.setFuncConfig(funcMapper.funcEntityListToFuncInfoList(allFunc));
        }

        // 上传依赖到制定节点路径
        if (realWork.getLibConfig() != null) {
            List<FileEntity> libFile =
                fileRepository.findAllById(JSON.parseArray(realWork.getLibConfig(), String.class));
            libFile.forEach(e -> {
                try {
                    scpJar(scpFileEngineNodeDto, fileDir + File.separator + e.getId(),
                        engineNode.getAgentHomePath() + "/zhiqingyun-agent/file/" + e.getId() + ".jar");
                } catch (JSchException | SftpException | InterruptedException | IOException ex) {
                    throw new IsxAppException(
                        LocalDateTime.now() + WorkLog.ERROR_INFO + "自定义依赖jar文件上传失败，请检查文件是否上传或者重新上传\n");
                }
            });
            executeReq.setLibConfig(JSON.parseArray(realWork.getLibConfig(), String.class));
        }

        // 构建作业完成，并打印作业配置信息
        submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("构建作业完成 \n");
        sparkConfig.forEach((k, v) -> submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append(k)
            .append(":").append(v).append(" \n"));
        submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("开始提交作业  \n");
        realWork = updateRealSubmitLog(realWork, submitLog);

        // 开始提交作业
        BaseResponse<?> baseResponse;
        RunWorkRes submitWorkRes;
        try {
            baseResponse =
                HttpUtils.doPost(genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.SUBMIT_WORK_URL),
                    executeReq, BaseResponse.class);
            if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
                throw new IsxAppException(
                    LocalDateTime.now() + WorkLog.ERROR_INFO + "提交作业失败 : " + baseResponse.getMsg() + "\n");
            }
            // 解析返回对象,获取appId
            if (baseResponse.getData() == null) {
                throw new IsxAppException(
                    LocalDateTime.now() + WorkLog.ERROR_INFO + "提交作业失败 : " + baseResponse.getMsg() + "\n");
            }
            submitWorkRes =
                JSON.parseObject(com.alibaba.fastjson.JSON.toJSONString(baseResponse.getData()), RunWorkRes.class);
            submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("提交作业成功:")
                .append(submitWorkRes.getAppId()).append("\n");
        } catch (ResourceAccessException e) {
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "提交作业失败:" + e.getMessage() + "\n ");
        } catch (HttpServerErrorException e1) {
            if (HttpStatus.BAD_GATEWAY.value() == e1.getRawStatusCode()) {
                throw new IsxAppException(
                    LocalDateTime.now() + WorkLog.ERROR_INFO + "提交作业失败 : 无法访问节点服务器,请检查服务器防火墙或者计算集群\n");
            }
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "提交作业失败 : " + e1.getMessage() + "\n");
        }

        // 提交作业成功后，开始循环判断状态
        String oldStatus = "";
        while (true) {

            // 获取作业状态并保存
            GetWorkStatusReq getWorkStatusReq = GetWorkStatusReq.builder().appId(submitWorkRes.getAppId())
                .clusterType(calculateEngineEntityOptional.get().getClusterType())
                .sparkHomePath(executeReq.getSparkHomePath()).build();
            baseResponse = HttpUtils.doPost(
                genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.GET_WORK_STATUS_URL),
                getWorkStatusReq, BaseResponse.class);
            log.debug("获取远程获取状态日志:{}", baseResponse.toString());

            if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
                throw new IsxAppException(
                    LocalDateTime.now() + WorkLog.ERROR_INFO + "获取作业状态异常 : " + baseResponse.getMsg() + "\n");
            }

            // 解析返回状态，并保存
            RunWorkRes workStatusRes = JSON.parseObject(JSON.toJSONString(baseResponse.getData()), RunWorkRes.class);

            // 状态发生变化，则添加日志状态
            if (!oldStatus.equals(workStatusRes.getAppStatus())) {
                submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("运行状态:")
                    .append(workStatusRes.getAppStatus()).append("\n");
            }
            oldStatus = workStatusRes.getAppStatus();
            realWork = updateRealSubmitLog(realWork, submitLog);

            // 如果状态是运行中，更新日志，继续执行
            List<String> runningStatus = Arrays.asList("RUNNING");
            List<String> deployingStatus = Arrays.asList("UNDEFINED", "SUBMITTED", "CONTAINERCREATING", "PENDING");
            if (deployingStatus.contains(workStatusRes.getAppStatus().toUpperCase())) {
                try {
                    Thread.sleep(4000);
                } catch (InterruptedException e) {
                    throw new IsxAppException(
                        LocalDateTime.now() + WorkLog.ERROR_INFO + "睡眠线程异常 : " + e.getMessage() + "\n");
                }
            } else if (runningStatus.contains(workStatusRes.getAppStatus().toUpperCase())) {
                submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("运行状态:").append("运行中 \n");
                updateRealSubmitLog(realWork, submitLog);
                return RunRealWorkRes.builder().appId(submitWorkRes.getAppId()).build();
            } else {
                // 任务运行错误
                // 获取运行失败的日志
                GetWorkStderrLogReq getWorkStderrLogReq = GetWorkStderrLogReq.builder().appId(workStatusRes.getAppId())
                    .clusterType(calculateEngineEntityOptional.get().getClusterType())
                    .sparkHomePath(engineNode.getSparkHomePath()).build();
                BaseResponse<?> baseResponseError = HttpUtils.doPost(
                    genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.GET_WORK_STDERR_LOG_URL),
                    getWorkStderrLogReq, BaseResponse.class);
                if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponseError.getCode())) {
                    throw new IsxAppException(
                        LocalDateTime.now() + WorkLog.ERROR_INFO + "获取作业日志异常 : " + baseResponseError.getMsg() + "\n");
                }

                // 解析日志并保存
                GetWorkStderrLogRes yagGetLogRes = com.alibaba.fastjson.JSON.parseObject(
                    com.alibaba.fastjson.JSON.toJSONString(baseResponseError.getData()), GetWorkStderrLogRes.class);
                realWork.setRunningLog(realWork.getRunningLog() + yagGetLogRes.getLog());
                updateRealSubmitLog(realWork, submitLog);

                throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "任务运行异常" + "\n");
            }
        }
    }

    public Map<String, String> genSparkSubmitConfig(Map<String, String> sparkConfig) {

        // 过滤掉，前缀不包含spark.xxx的配置，spark submit中必须都是spark.xxx
        Map<String, String> sparkSubmitConfig = new HashMap<>();
        sparkConfig.forEach((k, v) -> {
            if (k.startsWith("spark")) {
                sparkSubmitConfig.put(k, v);
            }
        });
        return sparkSubmitConfig;
    }

    public String genHttpUrl(String host, String port, String path) {

        String httpProtocol = isxAppProperties.isUseSsl() ? "https://" : "http://";
        String httpHost = isxAppProperties.isUsePort() ? host + ":" + port : host;

        return httpProtocol + httpHost + path;
    }

}
