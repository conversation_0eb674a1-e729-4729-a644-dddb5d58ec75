package com.isxcode.star.vip.modules.layer.service;

import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.modules.layer.entity.LayerEntity;
import com.isxcode.star.modules.layer.repository.LayerRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class LayerService {

    private final LayerRepository layerRepository;

    public LayerEntity getLayer(String layerId) {

        return layerRepository.findById(layerId).orElseThrow(() -> new IsxAppException("数据分层不存在"));
    }

    public String getLayerName(String layerId) {

        LayerEntity layer = layerRepository.findById(layerId).orElse(null);
        return layer == null ? layerId : layer.getName();
    }

    public String getLayerFullPathName(String layerId) {

        LayerEntity layer = layerRepository.findById(layerId).orElse(null);
        return layer == null ? layerId
            : layer.getParentNameList() == null ? layer.getName() : layer.getParentNameList() + "." + layer.getName();
    }
}
