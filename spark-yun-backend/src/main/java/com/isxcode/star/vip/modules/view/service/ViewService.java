package com.isxcode.star.vip.modules.view.service;

import com.alibaba.fastjson.JSON;
import com.isxcode.star.api.view.dto.CardInfo;
import com.isxcode.star.api.view.dto.DataSql;
import com.isxcode.star.api.view.dto.EchartOption;
import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.modules.view.entity.ViewCardEntity;
import com.isxcode.star.modules.view.entity.ViewEntity;
import com.isxcode.star.modules.view.mapper.ViewMapper;
import com.isxcode.star.modules.view.repository.ViewCardRepository;
import com.isxcode.star.modules.view.repository.ViewRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ViewService {

    private final ViewCardRepository viewCardRepository;

    private final ViewRepository viewRepository;

    private final ViewMapper viewMapper;

    public ViewCardEntity getViewCard(String viewCardId) {

        return viewCardRepository.findById(viewCardId).orElseThrow(() -> new IsxAppException("卡片不存在"));
    }

    public ViewEntity getView(String viewId) {

        return viewRepository.findById(viewId).orElseThrow(() -> new IsxAppException("大屏不存在"));
    }

    public CardInfo getViewCardInfo(String viewCardId) {

        ViewCardEntity viewCard = getViewCard(viewCardId);
        CardInfo cardInfo = viewMapper.viewCardEntityToCardInfo(viewCard);

        // 翻译前端配置
        if (!Strings.isEmpty(viewCard.getWebConfig())) {
            cardInfo.setWebConfig(JSON.parseObject(viewCard.getWebConfig()));
        }

        if (!Strings.isEmpty(viewCard.getDataSql())) {
            cardInfo.setDataSql(JSON.parseObject(viewCard.getDataSql(), DataSql.class));
        }

        cardInfo.setExampleData(JSON.parseObject(viewCard.getExampleData(), EchartOption.class));

        return cardInfo;
    }

}
