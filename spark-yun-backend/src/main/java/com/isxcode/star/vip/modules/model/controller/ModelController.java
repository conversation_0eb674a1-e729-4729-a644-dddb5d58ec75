package com.isxcode.star.vip.modules.model.controller;

import com.isxcode.star.api.main.constants.ModuleVipCode;
import com.isxcode.star.api.model.req.*;
import com.isxcode.star.api.model.res.AddColumnFormatRes;
import com.isxcode.star.api.model.res.ColumnFormatPageRes;
import com.isxcode.star.api.model.res.DataModelColumnPageRes;
import com.isxcode.star.api.model.res.DataModelPageRes;
import com.isxcode.star.api.user.constants.RoleType;
import com.isxcode.star.common.annotations.successResponse.SuccessResponse;
import com.isxcode.star.vip.annotation.vip.VipApi;
import com.isxcode.star.vip.modules.model.service.ModelBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(name = "数据建模模块")
@RequestMapping(ModuleVipCode.VIP_MODEL)
@RestController
@RequiredArgsConstructor
public class ModelController {

    private final ModelBizService modelBizService;

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "新建字段标准接口")
    @PostMapping("/addColumnFormat")
    @SuccessResponse("创建成功")
    public AddColumnFormatRes addColumnFormat(@Valid @RequestBody AddColumnFormatReq addColumnFormatReq) {

        return modelBizService.addColumnFormat(addColumnFormatReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "更新字段标准接口")
    @PostMapping("/updateColumnFormat")
    @SuccessResponse("更新成功")
    public void updateColumnFormat(@Valid @RequestBody UpdateColumnFormatReq updateColumnFormatReq) {

        modelBizService.updateColumnFormat(updateColumnFormatReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页查询字段标准接口")
    @PostMapping("/pageColumnFormat")
    @SuccessResponse("查询成功")
    public Page<ColumnFormatPageRes> pageColumnFormat(@Valid @RequestBody PageColumnFormatReq pageColumnFormatReq) {

        return modelBizService.pageColumnFormat(pageColumnFormatReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "删除字段标准接口")
    @PostMapping("/deleteColumnFormat")
    @SuccessResponse("删除成功")
    public void deleteColumnFormat(@Valid @RequestBody DeleteColumnFormatReq deleteColumnFormatReq) {

        modelBizService.deleteColumnFormat(deleteColumnFormatReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "删除字段标准接口")
    @PostMapping("/enableColumnFormat")
    @SuccessResponse("启用成功")
    public void enableColumnFormat(@Valid @RequestBody EnableColumnFormatReq enableColumnFormatReq) {

        modelBizService.enableColumnFormat(enableColumnFormatReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "删除字段标准接口")
    @PostMapping("/disableColumnFormat")
    @SuccessResponse("禁用成功")
    public void disableColumnFormat(@Valid @RequestBody DisableColumnFormatReq disableColumnFormatReq) {

        modelBizService.disableColumnFormat(disableColumnFormatReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "新建数据模型接口")
    @PostMapping("/addDataModel")
    @SuccessResponse("创建成功")
    public void addDataModel(@Valid @RequestBody AddDataModelReq addDataModelReq) {

        modelBizService.addDataModel(addDataModelReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "更新数据模型接口")
    @PostMapping("/updateDataModel")
    @SuccessResponse("更新成功")
    public void updateDataModel(@Valid @RequestBody UpdateDataModelReq updateDataModelReq) {

        modelBizService.updateDataModel(updateDataModelReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页查询数据模型接口")
    @PostMapping("/pageDataModel")
    @SuccessResponse("查询成功")
    public Page<DataModelPageRes> pageDataModel(@Valid @RequestBody PageDataModelReq pageDataModelReq) {

        return modelBizService.pageDataModel(pageDataModelReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "重置数据模型接口")
    @PostMapping("/resetDataModel")
    @SuccessResponse("重置成功")
    public void resetDataModel(@Valid @RequestBody ResetDataModelReq resetDataModelReq) {

        modelBizService.resetDataModel(resetDataModelReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "删除数据模型接口")
    @PostMapping("/deleteDataModel")
    @SuccessResponse("删除成功")
    public void deleteDataModel(@Valid @RequestBody DeleteDataModelReq deleteDataModelReq) {

        modelBizService.deleteDataModel(deleteDataModelReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "新建模型字段接口")
    @PostMapping("/addDataModelColumn")
    @SuccessResponse("创建成功")
    public void addDataModelColumn(@Valid @RequestBody AddDataModelColumnReq addDataModelColumnReq) {

        modelBizService.addDataModelColumn(addDataModelColumnReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "更新模型字段接口")
    @PostMapping("/updateDataModelColumn")
    @SuccessResponse("更新成功")
    public void updateDataModelColumn(@Valid @RequestBody UpdateDataModelColumnReq updateDataModelColumnReq) {

        modelBizService.updateDataModelColumn(updateDataModelColumnReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "更新模型字段顺序接口")
    @PostMapping("/updateDataModelColumnIndex")
    @SuccessResponse("更新成功")
    public void updateDataModelColumnIndex(
        @Valid @RequestBody UpdateDataModelColumnIndexReq updateDataModelColumnIndexReq) {

        modelBizService.updateDataModelColumnIndex(updateDataModelColumnIndexReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页查询模型字段接口")
    @PostMapping("/pageDataModelColumn")
    @SuccessResponse("查询成功")
    public Page<DataModelColumnPageRes> pageDataModelColumn(
        @Valid @RequestBody PageDataModelColumnReq pageDataModelColumnReq) {

        return modelBizService.pageDataModelColumn(pageDataModelColumnReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "删除模型字段接口")
    @PostMapping("/deleteDataModelColumn")
    @SuccessResponse("删除成功")
    public void deleteDataModelColumn(@Valid @RequestBody DeleteColumnFormatReq deleteColumnFormatReq) {

        modelBizService.deleteDataModelColumn(deleteColumnFormatReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页查询分层下的数据模型接口")
    @PostMapping("/pageDataModelByLayer")
    @SuccessResponse("查询成功")
    public Page<DataModelPageRes> pageDataModelByLayer(
        @Valid @RequestBody PageDataModelByLayerReq pageDataModelByLayerReq) {

        return modelBizService.pageDataModelByLayer(pageDataModelByLayerReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "一键复制数据模型接口")
    @PostMapping("/copyDataModel")
    @SuccessResponse("复制成功")
    public void copyDataModel(@Valid @RequestBody CopyDataModelReq copyDataModelReq) {

        modelBizService.copyDataModel(copyDataModelReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "构建数据模型接口")
    @PostMapping("/buildDataModel")
    @SuccessResponse("构建成功")
    public void buildDataModel(@Valid @RequestBody BuildDataModelReq buildDataModelReq) {

        modelBizService.buildDataModel(buildDataModelReq);
    }
}
