package com.isxcode.star.vip.modules.work.service;

import static com.isxcode.star.common.config.CommonConfig.TENANT_ID;
import static com.isxcode.star.common.config.CommonConfig.USER_ID;
import static com.isxcode.star.modules.workflow.run.WorkflowUtils.genWorkRunContext;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.isxcode.star.api.work.constants.WorkStatus;
import com.isxcode.star.api.work.dto.CronConfig;
import com.isxcode.star.api.workflow.req.DeployWorkflowReq;
import com.isxcode.star.api.workflow.req.KillWorkflowReq;
import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.modules.work.entity.VipWorkVersionEntity;
import com.isxcode.star.modules.work.entity.WorkConfigEntity;
import com.isxcode.star.modules.work.entity.WorkEntity;
import com.isxcode.star.modules.work.repository.VipWorkVersionRepository;
import com.isxcode.star.modules.work.repository.WorkRepository;
import com.isxcode.star.modules.work.run.WorkRunContext;
import com.isxcode.star.modules.work.service.WorkConfigService;
import com.isxcode.star.modules.work.service.WorkService;
import com.isxcode.star.modules.work.service.biz.WorkConfigBizService;
import com.isxcode.star.modules.workflow.entity.WorkflowConfigEntity;
import com.isxcode.star.modules.workflow.entity.WorkflowEntity;
import com.isxcode.star.modules.workflow.repository.WorkflowRepository;
import com.isxcode.star.modules.workflow.service.WorkflowService;
import com.isxcode.star.vip.modules.work.quartz.QuartzAllFlowJob;
import com.isxcode.star.vip.modules.work.quartz.QuartzFlowJobContext;
import com.isxcode.star.vip.modules.work.quartz.QuartzSingleFlowJob;
import com.isxcode.star.vip.modules.work.quartz.QuartzWorkJob;
import com.isxcode.star.vip.modules.work.version.VipWorkVersionMapper;
import com.isxcode.star.modules.workflow.entity.WorkflowVersionEntity;
import com.isxcode.star.vip.modules.workflow.version.WorkflowVersionMapper;
import com.isxcode.star.modules.workflow.repository.WorkflowVersionRepository;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;
import javax.transaction.Transactional;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.quartz.*;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class VipWorkBizService {

    private final Scheduler scheduler;

    private final WorkRepository workRepository;

    private final VipWorkVersionMapper vipWorkVersionMapper;

    private final VipWorkVersionRepository vipWorkVersionRepository;

    private final WorkService workService;

    private final WorkConfigBizService workConfigBizService;

    private final WorkflowVersionMapper workflowVersionMapper;

    private final WorkflowVersionRepository workflowVersionRepository;

    private final WorkflowRepository workflowRepository;

    private final WorkConfigService workConfigService;

    private final WorkflowService workflowService;

    private final VipWorkService vipWorkService;

    public void deployWork(String workId) {

        // 获取作业信息
        WorkEntity work = workService.getWorkEntity(workId);

        // 获取作业配置信息
        WorkConfigEntity workConfig = workConfigService.getWorkConfigEntity(work.getConfigId());

        // 获取作业流信息
        WorkflowEntity workflow = workflowService.getWorkflow(work.getWorkflowId());

        // 获取作业流配置
        WorkflowConfigEntity workflowConfig = workflowService.getWorkflowConfig(workflow.getConfigId());

        // 如果作业流配置corn表达式不让发布
        CronConfig workflowCronConfig = JSON.parseObject(workflowConfig.getCronConfig(), CronConfig.class);
        if (workflowCronConfig.isEnable()) {
            throw new IsxAppException("工作流已配置发布，不能单独发布作业");
        }

        // 判断作业定时是否启动
        CronConfig workCronConfig = JSON.parseObject(workConfig.getCronConfig(), CronConfig.class);
        if (!workCronConfig.isEnable() && Strings.isEmpty(workCronConfig.getCron())) {
            throw new IsxAppException("作业调度未启动");
        }

        // 封装作业版本
        VipWorkVersionEntity workVersion =
            vipWorkVersionMapper.workConfigEntityToVipWorkVersionEntity(workConfig, work);
        workVersion = vipWorkVersionRepository.save(workVersion);

        // 停止发布中的作业
        if (WorkStatus.PUBLISHED.equals(work.getStatus()) && !Strings.isEmpty(work.getVersionId())) {
            try {
                scheduler.unscheduleJob(TriggerKey.triggerKey(work.getVersionId()));
            } catch (SchedulerException e) {
                log.error(e.getMessage(), e);
                throw new IsxAppException("停止作业失败");
            }
        }

        // 构建quartz的运行上下文
        WorkRunContext workRunContext = genWorkRunContext(workVersion);

        // 封装quartz请求对象
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("quartzWorkJobContext", JSON.toJSONString(workRunContext));

        // 创建quartz作业
        JobDetail jobDetail = JobBuilder.newJob(QuartzWorkJob.class).setJobData(jobDataMap).build();
        Trigger trigger = TriggerBuilder.newTrigger()
            .withSchedule(
                CronScheduleBuilder.cronSchedule(workCronConfig.getCron()).withMisfireHandlingInstructionDoNothing())
            .withIdentity(workVersion.getId()).build();

        // 触发quartz作业
        try {
            scheduler.scheduleJob(jobDetail, trigger);
            scheduler.start();
        } catch (SchedulerException e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("发布作业失败");
        }

        // 给作业配置新的版本id
        work.setVersionId(workVersion.getId());

        // 修改任务状态
        work.setStatus(WorkStatus.PUBLISHED);
        workRepository.save(work);
    }

    public void pauseWork(String workId) {

        WorkEntity work = workService.getWorkEntity(workId);

        try {
            scheduler.pauseTrigger(TriggerKey.triggerKey(work.getVersionId()));
        } catch (SchedulerException e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("发布作业失败");
        }

        work.setStatus(WorkStatus.PAUSED);
        workRepository.save(work);
    }

    public void resumeWork(String workId) {

        WorkEntity work = workService.getWorkEntity(workId);

        try {
            scheduler.resumeTrigger(TriggerKey.triggerKey(work.getVersionId()));
        } catch (SchedulerException e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("发布作业失败");
        }

        work.setStatus(WorkStatus.PUBLISHED);
        workRepository.save(work);
    }

    public void killWork(String workId) {

        WorkEntity work = workService.getWorkEntity(workId);

        try {
            scheduler.unscheduleJob(TriggerKey.triggerKey(work.getVersionId()));
        } catch (SchedulerException e) {
            log.error(e.getMessage());
            throw new IsxAppException("发布作业失败");
        }

        work.setStatus(WorkStatus.STOP);
        workRepository.save(work);
    }

    /**
     * 发布工作流.
     */
    public void deployWorkflow(DeployWorkflowReq deployWorkflowReq) {

        // 获取作业流信息
        WorkflowEntity workflow = workflowService.getWorkflow(deployWorkflowReq.getWorkflowId());

        // 获取作业流配置信息
        WorkflowConfigEntity workflowConfig = workflowService.getWorkflowConfig(workflow.getConfigId());

        // 如果作业流中没有作业,提示添加节点
        if (Strings.isEmpty(workflowConfig.getNodeList())
            || JSON.parseArray(workflowConfig.getNodeList(), String.class).isEmpty()) {
            throw new IsxAppException("作业流为空，请先拖拽作业");
        }

        // 获取调度信息
        CronConfig cronConfig = JSON.parseObject(workflowConfig.getCronConfig(), CronConfig.class);

        // 获取下一次开始执行时间
        LocalDateTime nexDateTime = null;
        if (cronConfig.isEnable()) {
            try {
                CronExpression cronExpression = new CronExpression(cronConfig.getCron());
                Date nextValidTimeAfter = cronExpression.getNextValidTimeAfter(new Date());
                nexDateTime = DateUtil.toLocalDateTime(nextValidTimeAfter);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }

        // 已发布作业流，不能发布
        if (WorkStatus.PUBLISHED.equals(workflow.getStatus())) {
            throw new IsxAppException("请先下线");
        }

        // 获取所有的作业信息
        List<String> workIds = JSONArray.parseArray(workflowConfig.getNodeList(), String.class);
        List<WorkEntity> allWork = workRepository.findAllById(workIds);

        // 区分两种调度模式
        if ("ALL".equals(cronConfig.getType())) {
            // 单一调度，只要作业流配置调度时间即可

            // 封装作业版本
            for (WorkEntity metaWork : allWork) {

                // 获取作业配置
                WorkConfigEntity workConfig = workConfigBizService.getWorkConfigEntity(metaWork.getConfigId());

                // 转成作业版本
                VipWorkVersionEntity workVersion =
                    vipWorkVersionMapper.workConfigEntityToVipWorkVersionEntity(workConfig, metaWork);
                workVersion.setAlarmList(workConfig.getAlarmList());

                // 持久化作业版本
                workVersion = vipWorkVersionRepository.save(workVersion);

                // 更新作业的版本信息
                metaWork.setVersionId(workVersion.getId());
                metaWork.setStatus(WorkStatus.PUBLISHED);
                workRepository.save(metaWork);
            }

            // 封装工作流版本
            WorkflowVersionEntity workflowVersion =
                workflowVersionMapper.workflowConfigEntityToVipWorkflowVersionEntity(workflowConfig, workflow);
            workflowVersion.setName(getWorkflowNextVersionName(workflow.getVersionId()));
            workflowVersion.setCronConfig(workflowConfig.getCronConfig());
            workflowVersion.setAlarmList(workflowConfig.getAlarmList());
            workflowVersion = workflowVersionRepository.save(workflowVersion);

            // 配置调度信息
            QuartzFlowJobContext quartzFlowJobRunContext = QuartzFlowJobContext.builder().userId(USER_ID.get())
                .tenantId(TENANT_ID.get()).flowVersionId(workflowVersion.getId()).build();

            // 封装调度请求对象
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put("quartzFlowJobRunContext", JSON.toJSONString(quartzFlowJobRunContext));

            // 创建调度作业
            if (cronConfig.isEnable()) {
                JobDetail jobDetail = JobBuilder.newJob(QuartzAllFlowJob.class).setJobData(jobDataMap).build();
                Trigger trigger = TriggerBuilder.newTrigger().withSchedule(
                    CronScheduleBuilder.cronSchedule(cronConfig.getCron()).withMisfireHandlingInstructionDoNothing())
                    .withIdentity(workflowVersion.getId()).build();

                // 触发调度作业
                try {
                    scheduler.scheduleJob(jobDetail, trigger);
                    scheduler.start();
                } catch (SchedulerException e) {
                    log.error(e.getMessage());
                    throw new IsxAppException("发布作业失败");
                }
                workflow.setNextDateTime(nexDateTime);
            }

            // 修改工作流状态和版本号
            workflow.setVersionId(workflowVersion.getId());
            workflow.setStatus(WorkStatus.PUBLISHED);
            workflowRepository.save(workflow);
        } else if ("SINGLE".equals(cronConfig.getType())) {

            // 离散调度，校验每个作业的调度时间是否配置
            for (WorkEntity metaWork : allWork) {
                WorkConfigEntity workConfig = workConfigService.getWorkConfigEntity(metaWork.getConfigId());
                CronConfig metaWorkConfig = JSON.parseObject(workConfig.getCronConfig(), CronConfig.class);
                if (!metaWorkConfig.isEnable()) {
                    throw new IsxAppException("【" + metaWork.getName() + "】未配置调度时间");
                }
            }

            // 封装作业版本
            for (WorkEntity metaWork : allWork) {

                // 获取作业配置
                WorkConfigEntity workConfig = workConfigBizService.getWorkConfigEntity(metaWork.getConfigId());

                // 转成作业版本
                VipWorkVersionEntity workVersion =
                    vipWorkVersionMapper.workConfigEntityToVipWorkVersionEntity(workConfig, metaWork);
                workVersion.setAlarmList(workflowConfig.getAlarmList());

                // 持久化作业版本
                workVersion = vipWorkVersionRepository.save(workVersion);

                // 更新作业的版本信息
                metaWork.setVersionId(workVersion.getId());
                metaWork.setStatus(WorkStatus.PUBLISHED);
                workRepository.save(metaWork);
            }

            // 封装工作流版本
            WorkflowVersionEntity workflowVersion =
                workflowVersionMapper.workflowConfigEntityToVipWorkflowVersionEntity(workflowConfig, workflow);
            workflowVersion.setName(getWorkflowNextVersionName(workflow.getVersionId()));
            workflowVersion.setCronConfig(workflowConfig.getCronConfig());
            workflowVersion.setAlarmList(workflowConfig.getAlarmList());
            workflowVersion = workflowVersionRepository.save(workflowVersion);

            // 配置调度信息
            QuartzFlowJobContext quartzFlowJobRunContext = QuartzFlowJobContext.builder().userId(USER_ID.get())
                .tenantId(TENANT_ID.get()).flowVersionId(workflowVersion.getId()).build();

            // 封装调度请求对象
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put("quartzFlowJobRunContext", JSON.toJSONString(quartzFlowJobRunContext));

            // 创建调度作业
            if (cronConfig.isEnable()) {
                JobDetail jobDetail = JobBuilder.newJob(QuartzSingleFlowJob.class).setJobData(jobDataMap).build();
                Trigger trigger = TriggerBuilder.newTrigger().withSchedule(
                    CronScheduleBuilder.cronSchedule(cronConfig.getCron()).withMisfireHandlingInstructionDoNothing())
                    .withIdentity(workflowVersion.getId()).build();

                // 触发调度作业
                try {
                    scheduler.scheduleJob(jobDetail, trigger);
                    scheduler.start();
                } catch (SchedulerException e) {
                    log.error(e.getMessage());
                    throw new IsxAppException("发布作业失败");
                }
                workflow.setNextDateTime(nexDateTime);
            }

            // 修改工作流状态和版本号
            workflow.setVersionId(workflowVersion.getId());
            workflow.setStatus(WorkStatus.PUBLISHED);
            workflowRepository.save(workflow);
        } else {
            throw new IsxAppException("调度模式暂不支持");
        }
    }

    public String getWorkflowNextVersionName(String prevWorkflowVersionId) {

        if (Strings.isEmpty(prevWorkflowVersionId)) {
            return "v0.0.1";
        }

        // 获取前一个工作流的版本名字
        WorkflowVersionEntity workflowVersion = workflowVersionRepository.findById(prevWorkflowVersionId).get();

        // 获取新的版本编号
        String[] vs = workflowVersion.getName().replace("v", "").split("\\.");
        int thirdNumber = Integer.parseInt(vs[2]) + 1;
        int secondNumber = Integer.parseInt(vs[1]);
        int firstNumber = Integer.parseInt(vs[0]);
        if (thirdNumber >= 10) {
            thirdNumber = thirdNumber - 10;
            secondNumber = secondNumber + 1;
        }
        if (secondNumber >= 10) {
            secondNumber = secondNumber - 10;
            firstNumber = firstNumber + 1;
        }
        return "v" + firstNumber + '.' + secondNumber + "." + thirdNumber;
    }

    public void killWorkflow(KillWorkflowReq killWorkflowReq) {

        // 判断作业流的调度类型，如果是单一类型，直接关闭作业流的调度
        WorkflowEntity workflow = workflowService.getWorkflow(killWorkflowReq.getWorkflowId());

        // 获取版本信息id，如果未发布直接结束
        String versionId = workflow.getVersionId();
        if (versionId == null) {
            throw new IsxAppException("请先发布");
        }

        WorkflowVersionEntity workflowVersion = vipWorkService.getWorkflowVersion(versionId);

        try {
            scheduler.unscheduleJob(TriggerKey.triggerKey(workflowVersion.getId()));
        } catch (SchedulerException e) {
            log.error(e.getMessage());
            throw new IsxAppException("发布作业失败");
        }

        workflow.setStatus(WorkStatus.STOP);
        workflow.setNextDateTime(null);
        workflowRepository.save(workflow);

        // 把相关的作业状态全部下线
        List<String> workIds = JSONArray.parseArray(workflowVersion.getNodeList(), String.class);
        List<WorkEntity> allWork = workRepository.findAllById(workIds);
        allWork.forEach(e -> {
            e.setStatus(WorkStatus.UN_PUBLISHED);
            workRepository.save(e);
        });
    }
}
