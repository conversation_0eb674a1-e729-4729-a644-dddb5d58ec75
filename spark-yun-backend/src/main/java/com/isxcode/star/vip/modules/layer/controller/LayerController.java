package com.isxcode.star.vip.modules.layer.controller;

import com.isxcode.star.api.layer.req.*;
import com.isxcode.star.api.layer.res.GetParentParentLayerRes;
import com.isxcode.star.api.layer.res.LayerPageRes;
import com.isxcode.star.api.layer.res.RecursiveLayerRes;
import com.isxcode.star.api.main.constants.ModuleVipCode;
import com.isxcode.star.api.user.constants.RoleType;
import com.isxcode.star.common.annotations.successResponse.SuccessResponse;
import com.isxcode.star.vip.annotation.vip.VipApi;
import com.isxcode.star.vip.modules.layer.service.LayerBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(name = "数据分层模块")
@RequestMapping(ModuleVipCode.VIP_LAYER)
@RestController
@RequiredArgsConstructor
public class LayerController {

    private final LayerBizService layerBizService;

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "新建数据分层接口")
    @PostMapping("/addLayer")
    @SuccessResponse("创建成功")
    public void addForm(@Valid @RequestBody AddLayerReq addLayerReq) {

        layerBizService.addLayer(addLayerReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "更新分层接口")
    @PostMapping("/updateLayer")
    @SuccessResponse("更新成功")
    public void updateLayer(@Valid @RequestBody UpdateLayerReq updateLayerReq) {

        layerBizService.updateLayer(updateLayerReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页查询数据分层接口")
    @PostMapping("/pageLayer")
    @SuccessResponse("查询成功")
    public Page<LayerPageRes> pageLayer(@Valid @RequestBody PageLayerReq pageLayerReq) {

        return layerBizService.pageLayer(pageLayerReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "删除数据分层接口")
    @PostMapping("/deleteLayer")
    @SuccessResponse("删除成功")
    public void deleteLayer(@Valid @RequestBody DeleteLayerReq deleteLayerReq) {

        layerBizService.deleteLayer(deleteLayerReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页搜索数据分层接口")
    @PostMapping("/searchLayer")
    @SuccessResponse("查询成功")
    public Page<LayerPageRes> searchLayer(@Valid @RequestBody SearchLayerReq searchLayerReq) {

        return layerBizService.searchLayer(searchLayerReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "获取父父级分层信息")
    @PostMapping("/getParentParentLayer")
    @SuccessResponse("获取成功")
    public GetParentParentLayerRes getParentParentLayer(
        @Valid @RequestBody GetParentParentLayerReq getParentParentLayerReq) {

        return layerBizService.getParentParentLayer(getParentParentLayerReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "递归查询分层信息接口")
    @PostMapping("/recursiveLayer")
    @SuccessResponse("获取成功")
    public RecursiveLayerRes recursiveLayer(@Valid @RequestBody RecursiveLayerReq recursiveLayerReq) {

        return layerBizService.recursiveLayer(recursiveLayerReq);
    }
}
