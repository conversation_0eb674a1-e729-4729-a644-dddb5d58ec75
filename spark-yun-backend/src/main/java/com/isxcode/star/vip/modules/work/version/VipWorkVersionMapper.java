package com.isxcode.star.vip.modules.work.version;

import com.isxcode.star.modules.work.entity.VipWorkVersionEntity;
import com.isxcode.star.modules.work.entity.WorkConfigEntity;
import com.isxcode.star.modules.work.entity.WorkEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface VipWorkVersionMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "createDateTime", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "lastModifiedDateTime", ignore = true)
    @Mapping(target = "versionNumber", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "workId", source = "workEntity.id")
    @Mapping(target = "workType", source = "workEntity.workType")
    VipWorkVersionEntity workConfigEntityToVipWorkVersionEntity(WorkConfigEntity workConfigEntity,
        WorkEntity workEntity);
}
