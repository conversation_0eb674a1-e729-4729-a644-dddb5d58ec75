package com.isxcode.star.vip.modules.form.service;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.isxcode.star.api.datasource.constants.ColumnType;
import com.isxcode.star.api.datasource.constants.DatasourceType;
import com.isxcode.star.api.datasource.dto.ConnectInfo;
import com.isxcode.star.api.datasource.dto.SecurityColumnDto;
import com.isxcode.star.api.form.constants.FormComponentType;
import com.isxcode.star.api.form.constants.FormCreateMode;
import com.isxcode.star.api.form.constants.FormStatus;
import com.isxcode.star.api.form.dto.FormComponentDto;
import com.isxcode.star.api.form.req.*;
import com.isxcode.star.api.form.res.*;
import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.backend.api.base.properties.IsxAppProperties;
import com.isxcode.star.common.utils.jwt.JwtUtils;
import com.isxcode.star.modules.datasource.entity.DatasourceEntity;
import com.isxcode.star.modules.datasource.mapper.DatasourceMapper;
import com.isxcode.star.modules.datasource.service.DatasourceService;
import com.isxcode.star.modules.datasource.source.DataSourceFactory;
import com.isxcode.star.modules.datasource.source.Datasource;
import com.isxcode.star.modules.form.entity.FormComponentEntity;
import com.isxcode.star.modules.form.entity.FormEntity;
import com.isxcode.star.modules.form.entity.FormLinkEntity;
import com.isxcode.star.modules.form.mapper.FormMapper;
import com.isxcode.star.modules.form.repository.FormComponentRepository;
import com.isxcode.star.modules.form.repository.FormLinkRepository;
import com.isxcode.star.modules.form.repository.FormRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Date;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.isxcode.star.common.config.CommonConfig.*;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class FormBizService {

    private final DatasourceService datasourceService;

    private final FormMapper formMapper;

    private final FormRepository formRepository;

    private final FormComponentRepository formComponentRepository;

    private final FormService formService;

    private final IsxAppProperties isxAppProperties;

    private final FormLinkRepository formLinkRepository;

    private final DataSourceFactory dataSourceFactory;

    private final DatasourceMapper datasourceMapper;

    public AddFormRes addForm(AddFormReq addFormReq) {

        // 如果是制定表需要判断mainTable是否为空
        if (FormCreateMode.EXIST_TABLE.equals(addFormReq.getCreateMode())
            || FormCreateMode.CREATE_TABLE.equals(addFormReq.getCreateMode())) {
            if (Strings.isEmpty(addFormReq.getMainTable())) {
                throw new IsxAppException("MainTable不能为空");
            }
        }

        // 自动创建表，自动生成表名
        if (FormCreateMode.AUTO_TABLE.equals(addFormReq.getCreateMode())) {
            addFormReq.setMainTable("SY_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16));
        }

        // 判断数据是否存在
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(addFormReq.getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());

        // 仅支持mysql
        if (!DatasourceType.MYSQL.equals(datasource.getDataSourceType())
            && !DatasourceType.H2.equals(datasource.getDataSourceType())) {
            throw new IsxAppException("当前仅支持Mysql和H2数据源");
        }

        // 判断表是否存在
        if (FormCreateMode.CREATE_TABLE.equals(addFormReq.getCreateMode())) {
            boolean tableIsExist = datasource.tableIsExist(connectInfo, addFormReq.getMainTable());
            if (FormCreateMode.EXIST_TABLE.equals(addFormReq.getCreateMode()) && !tableIsExist) {
                throw new IsxAppException("该表已存在，无法新建");
            }
        }

        // 创建表
        if (FormCreateMode.CREATE_TABLE.equals(addFormReq.getCreateMode())
            || FormCreateMode.AUTO_TABLE.equals(addFormReq.getCreateMode())) {
            String createTableTemplate = "create table if not exists %s\n" + "(\n"
                + "    sy_id                     varchar(200)         not null comment '主键id' primary key,\n"
                + "    deleted                   int        default 0 not null comment '逻辑删除',\n"
                + "    create_by                 varchar(200)         not null comment '创建者',\n"
                + "    create_date_time          datetime             not null comment '创建时间',\n"
                + "    last_modified_by          varchar(200)         not null comment '最近更新者',\n"
                + "    last_modified_date_time   datetime             not null comment '最近更新时间',\n"
                + "    version_number            int        default 1 not null comment '版本号'\n" + ")";
            datasource.executeSql(connectInfo, String.format(createTableTemplate, addFormReq.getMainTable()));
        }

        // 判断表单名称不重复
        Optional<FormEntity> formByName = formRepository.findByName(addFormReq.getName());
        if (formByName.isPresent()) {
            throw new IsxAppException("表单名称重复");
        }

        FormEntity formEntity = formMapper.addFormReqToFormEntity(addFormReq);

        // 表单初始状态为未发布
        formEntity.setStatus(FormStatus.UNPUBLISHED);

        // 初始化表单版本号
        formEntity.setFormVersion(UUID.randomUUID().toString());
        formEntity = formRepository.save(formEntity);

        return formMapper.formEntityToAddFormRes(formEntity);
    }

    public void updateForm(UpdateFormReq updateFormReq) {

        // 判断表单是否存在
        FormEntity formEntity = formService.getForm(updateFormReq.getId());

        formEntity.setName(updateFormReq.getName());
        formEntity.setRemark(updateFormReq.getRemark());

        formRepository.save(formEntity);
    }

    public void saveFormConfig(SaveFormConfigReq saveFormConfigReq) {

        // 判断表单是否存在
        FormEntity formEntity = formService.getForm(saveFormConfigReq.getFormId());

        // 如果作业发布了，不能直接编辑
        if (FormStatus.PUBLISHED.equals(formEntity.getStatus())) {
            throw new IsxAppException("请下线后再编辑");
        }

        // 不存在的字段新建
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(formEntity.getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());

        try (Connection connection = datasource.getConnection(connectInfo);
            Statement statement = connection.createStatement()) {
            DatabaseMetaData metaData = connection.getMetaData();
            for (int i = 0; i < saveFormConfigReq.getComponents().size(); i++) {
                FormComponentDto formComponentDto = saveFormConfigReq.getComponents().get(i);
                if (FormComponentType.FORM_STATIC_PLACEHOLDER.equals(formComponentDto.getComponentType())
                    || FormComponentType.FORM_STATIC_EMPTY.equals(formComponentDto.getComponentType())) {
                    continue;
                }
                ResultSet columns = metaData.getColumns(datasource.parseDbName(datasourceEntity.getJdbcUrl()), null,
                    formEntity.getMainTable(), formComponentDto.getFormValueCode());
                if (!columns.next()) {
                    String addColumnSql = "alter table " + formEntity.getMainTable() + " add " + "`"
                        + formComponentDto.getFormValueCode() + "` "
                        + formService.getColumnType(saveFormConfigReq.getComponents().get(i).getComponentType())
                        + " null";
                    try {
                        statement.execute(addColumnSql);
                    } catch (SQLException exception) {
                        if (exception.getErrorCode() == 42121) {
                            continue;
                        }
                        log.error(exception.getMessage(), exception);
                        if (exception.getErrorCode() == 1064) {
                            throw new IsxAppException(formComponentDto.getFormValueCode() + "字段，可能为关键字或者字符异常");
                        }
                        throw new IsxAppException("创建数据库字段异常");
                    }
                }
            }
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("无法链接数据源");
        }

        // 对组件重新组装
        Map<String, FormComponentDto> componentMap = saveFormConfigReq.getComponents().stream()
            .collect(Collectors.toMap(FormComponentDto::getUuid, Function.identity()));

        // 把前端的配置持久化
        formEntity.setFormWebConfig(JSON.toJSONString(saveFormConfigReq.getComponents()));
        formRepository.save(formEntity);

        // 遍历新的组件id
        List<String> newComponentIdList =
            saveFormConfigReq.getComponents().stream().map(FormComponentDto::getUuid).collect(Collectors.toList());

        // 需要删除的组件
        List<FormComponentEntity> oldComponentList = formComponentRepository.findAllByFormId(formEntity.getId());
        List<String> oldComponentIdList =
            oldComponentList.stream().map(FormComponentEntity::getUuid).collect(Collectors.toList());
        List<FormComponentEntity> needDeleteComponentList = oldComponentList.stream()
            .filter(e -> !newComponentIdList.contains(e.getUuid())).collect(Collectors.toList());
        formComponentRepository.deleteAll(needDeleteComponentList);

        // 需要更新的组件
        List<FormComponentEntity> needUpdateComponentList = oldComponentList.stream()
            .filter(e -> newComponentIdList.contains(e.getUuid())).collect(Collectors.toList());
        needUpdateComponentList.forEach(e -> {
            e.setComponentConfig(JSON.toJSONString(componentMap.get(e.getUuid())));
        });
        formComponentRepository.saveAll(needUpdateComponentList);

        // 需要新增的组件
        List<FormComponentDto> needAddComponentDtoList = saveFormConfigReq.getComponents().stream()
            .filter(e -> !oldComponentIdList.contains(e.getUuid())).collect(Collectors.toList());
        List<FormComponentEntity> needAddComponentList = new ArrayList<>();
        needAddComponentDtoList.forEach(e -> {
            FormComponentEntity formComponentEntity = new FormComponentEntity();
            formComponentEntity.setComponentConfig(JSON.toJSONString(e));
            formComponentEntity.setUuid(e.getUuid());
            formComponentEntity.setFormId(formEntity.getId());
            needAddComponentList.add(formComponentEntity);
        });
        formComponentRepository.saveAll(needAddComponentList);

        // 过滤非逻辑字段
        List<FormComponentDto> components = saveFormConfigReq.getComponents().stream()
            .filter(e -> !e.getComponentType().equals(FormComponentType.FORM_STATIC_PLACEHOLDER)
                && !e.getComponentType().equals(FormComponentType.FORM_STATIC_EMPTY))
            .collect(Collectors.toList());

        // insertSql自动生成
        String insertSql =
            generateInsertSql(formEntity.getMainTable(), components, formEntity.getCreateMode(), datasource);
        formEntity.setInsertSql(insertSql);

        // deleteSql自动生成
        String deleteSql =
            generateDeleteSql(formEntity.getMainTable(), components, formEntity.getCreateMode(), datasource);
        formEntity.setDeleteSql(deleteSql);

        // updateSql自动生成
        String updateSql =
            generateUpdateSql(formEntity.getMainTable(), components, formEntity.getCreateMode(), datasource);
        formEntity.setUpdateSql(updateSql);

        // selectSql自动生成
        String selectSql =
            generateSelectSql(formEntity.getMainTable(), components, formEntity.getCreateMode(), datasource);
        formEntity.setSelectSql(selectSql);

        // 配置新的版本号
        formEntity.setFormVersion(UUID.randomUUID().toString());
        formRepository.save(formEntity);
    }

    private static String generateInsertSql(String mainTable, List<FormComponentDto> components, String createMode,
        Datasource datasource) {

        String insertSqlTemplate = "INSERT INTO %s ( %s ) VALUES ( %s )";

        List<String> columns =
            components.stream().map(e -> "`" + e.getFormValueCode() + "`").collect(Collectors.toList());
        if (FormCreateMode.CREATE_TABLE.equals(createMode) || FormCreateMode.AUTO_TABLE.equals(createMode)) {
            columns.add("sy_id");
            columns.add("create_by");
            columns.add("create_date_time");
            columns.add("last_modified_by");
            columns.add("last_modified_date_time");
        }

        List<String> values = components.stream().map(FormComponentDto::getUuid).map(key -> "'${" + key + "}'")
            .collect(Collectors.toList());
        if (FormCreateMode.CREATE_TABLE.equals(createMode) || FormCreateMode.AUTO_TABLE.equals(createMode)) {
            values.add("'${sy_id}'");
            values.add("'${create_by}'");
            values.add("'${create_date_time}'");
            values.add("'${last_modified_by}'");
            values.add("'${last_modified_date_time}'");
        }

        return String.format(insertSqlTemplate, mainTable, StringUtils.join(columns, ","),
            StringUtils.join(values, ","));
    }

    private static String generateDeleteSql(String mainTable, List<FormComponentDto> components, String createMode,
        Datasource datasource) {

        String deleteSqlTemplate = "DELETE FROM %s WHERE %s ";

        List<String> conditions = new ArrayList<>();

        if (FormCreateMode.CREATE_TABLE.equals(createMode) || FormCreateMode.AUTO_TABLE.equals(createMode)) {
            conditions.add("sy_id = '${sy_id}'");
        } else {
            boolean hasPrimaryColumn =
                components.stream().anyMatch(component -> Boolean.TRUE.equals(component.getIsPrimaryColumn()));
            if (hasPrimaryColumn) {
                conditions = components.stream().filter(e -> e.getIsPrimaryColumn() != null && e.getIsPrimaryColumn())
                    .map(component -> component.getFormValueCode() + " = '${" + component.getUuid() + "}'")
                    .collect(Collectors.toList());
            } else {
                // 如果isPrimaryKey没有指定，则以所有的字段为删除条件
                conditions = components.stream()
                    .map(component -> component.getFormValueCode() + " = '${" + component.getUuid() + "}'")
                    .collect(Collectors.toList());
            }
        }

        return String.format(deleteSqlTemplate, mainTable, StringUtils.join(conditions, " AND "));
    }

    private static String generateUpdateSql(String mainTable, List<FormComponentDto> components, String createMode,
        Datasource datasource) {

        String updateSqlTemplate = "UPDATE %s SET '${UPDATE_COLUMN}' WHERE %s";

        List<String> conditions = new ArrayList<>();

        if (FormCreateMode.CREATE_TABLE.equals(createMode) || FormCreateMode.AUTO_TABLE.equals(createMode)) {
            conditions.add("sy_id = '${sy_id}'");
        } else {
            boolean hasPrimaryColumn =
                components.stream().anyMatch(component -> Boolean.TRUE.equals(component.getIsPrimaryColumn()));
            if (hasPrimaryColumn) {
                conditions = components.stream().filter(e -> e.getIsPrimaryColumn() != null && e.getIsPrimaryColumn())
                    .map(component -> component.getFormValueCode() + " = '${" + component.getUuid() + "}'")
                    .collect(Collectors.toList());
            } else {
                // 如果isPrimaryKey没有指定，则以所有的字段为更新条件
                conditions = components.stream()
                    .map(component -> component.getFormValueCode() + " = '${" + component.getUuid() + "}'")
                    .collect(Collectors.toList());
            }

        }

        return String.format(updateSqlTemplate, mainTable, StringUtils.join(conditions, " AND "));
    }

    private static String generateSelectSql(String mainTable, List<FormComponentDto> components, String createMode,
        Datasource datasource) {

        String selectSqlTemplate = "SELECT %s FROM %s ";

        List<String> columns = components.stream()
            .map(component -> "`" + component.getFormValueCode() + "`" + " as '" + component.getUuid() + "'")
            .collect(Collectors.toList());

        if (FormCreateMode.CREATE_TABLE.equals(createMode) || FormCreateMode.AUTO_TABLE.equals(createMode)) {
            columns.add("sy_id");
        }

        String selectSql = String.format(selectSqlTemplate, StringUtils.join(columns, ","), mainTable);

        return DatasourceType.H2.equals(datasource.getDataSourceType()) ? selectSql.replace("\'", "\"") : selectSql;
    }

    public GetFormConfigRes getFormConfig(GetFormConfigReq getFormConfigReq) {

        FormEntity form = formService.getForm(getFormConfigReq.getFormId());

        List<FormComponentDto> formComponentDtos = JSON.parseArray(form.getFormWebConfig(), FormComponentDto.class);

        return new GetFormConfigRes(form.getId(), formComponentDtos, form.getDatasourceId(), form.getStatus(),
            form.getMainTable(), form.getFormVersion());
    }

    public GetFormConfigForAnonymousRes getFormConfigForAnonymous(
        GetFormConfigForAnonymousReq getFormConfigForAnonymousReq) {

        FormEntity form = formService.getForm(getFormConfigForAnonymousReq.getFormId());

        // 判断表单是否发布
        if (!FormStatus.PUBLISHED.equals(form.getStatus())) {
            throw new IsxAppException("表单未发布，无法访问");
        }

        List<FormComponentDto> formComponentDtos = JSON.parseArray(form.getFormWebConfig(), FormComponentDto.class);

        return new GetFormConfigForAnonymousRes(form.getId(), formComponentDtos, form.getDatasourceId(),
            form.getStatus(), form.getMainTable(), form.getFormVersion());
    }

    public Page<FormPageRes> pageForm(PageFormReq pageFormReq) {

        Page<FormEntity> formPage = formRepository.pageForm(pageFormReq.getSearchKeyWord(),
            PageRequest.of(pageFormReq.getPage(), pageFormReq.getPageSize()));

        Page<FormPageRes> result = formPage.map(formMapper::formEntityToFormPageRes);

        // 翻译数据源的名字
        result.getContent().forEach(e -> e.setDatasourceName(datasourceService.getDatasourceName(e.getDatasourceId())));
        return result;
    }

    public void addData(AddDataReq addDataReq) {

        // 获取并校验表单
        FormEntity form = formService.getForm(addDataReq.getFormId());

        // 校验表单是否发布
        if (!FormStatus.PUBLISHED.equals(form.getStatus())) {
            throw new IsxAppException("请先发布表单");
        }

        // 校验表单版本号是否一致
        if (!form.getFormVersion().equals(addDataReq.getFormVersion())) {
            throw new IsxAppException("表单已重新发布，请刷新页面");
        }

        // 获取表单中的每个组件
        Map<String, FormComponentDto> componentMap = formService.getComponentMap(form.getId());

        // 数据源
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(form.getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());

        // 解析sql中的字段顺序
        List<SecurityColumnDto> securityColumns = datasourceService.transSecurityColumns(form.getInsertSql());

        // 将普通sql转成安全sql
        String securityExecuteSql = datasourceService.transSecuritySql(form.getInsertSql());

        // 将用户请求值转移到securityColumns中
        formService.SetReqValueToSecurityColumns(componentMap, securityColumns, addDataReq.getData());

        // 补充系统字段
        if (FormCreateMode.CREATE_TABLE.equals(form.getCreateMode())
            || FormCreateMode.AUTO_TABLE.equals(form.getCreateMode())) {
            securityColumns.forEach(e -> {
                String columnName = e.getName().substring(3);
                switch (columnName) {
                    case "sy_id":
                        Snowflake snowflake = IdUtil.getSnowflake();
                        e.setValue("sy_" + snowflake.nextIdStr());
                        e.setType(ColumnType.STRING);
                        break;
                    case "create_by":
                    case "last_modified_by":
                        e.setValue(USER_ID.get());
                        e.setType(ColumnType.STRING);
                        break;
                    case "create_date_time":
                    case "last_modified_date_time":
                        e.setValue(new Date().getTime());
                        e.setType(ColumnType.TIMESTAMP);
                        break;
                }
            });
        }

        // 执行安全sql
        datasource.securityExecuteSql(connectInfo, securityExecuteSql, securityColumns);
    }

    public void deleteData(DeleteDataReq deleteDataReq) {

        // 获取并校验表单
        FormEntity form = formService.getForm(deleteDataReq.getFormId());

        // 校验表单是否发布
        if (!FormStatus.PUBLISHED.equals(form.getStatus())) {
            throw new IsxAppException("请先发布表单");
        }

        // 校验表单版本号是否一致
        if (!form.getFormVersion().equals(deleteDataReq.getFormVersion())) {
            throw new IsxAppException("表单已重新发布，请刷新页面");
        }

        // 获取组件Map结构
        Map<String, FormComponentDto> componentMap = formService.getComponentMap(form.getId());

        // 数据源
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(form.getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());

        // 解析sql中的字段顺序
        List<SecurityColumnDto> securityColumns = datasourceService.transSecurityColumns(form.getDeleteSql());

        // 将普通sql转成安全sql
        String securityExecuteSql = datasourceService.transSecuritySql(form.getDeleteSql());

        // 将用户请求值转移到securityColumns中
        formService.SetReqValueToSecurityColumns(componentMap, securityColumns, deleteDataReq.getData());

        // 补充系统字段
        if (FormCreateMode.CREATE_TABLE.equals(form.getCreateMode())
            || FormCreateMode.AUTO_TABLE.equals(form.getCreateMode())) {
            securityColumns.forEach(e -> {
                String columnName = e.getName().substring(3);
                switch (columnName) {
                    case "sy_id":
                        e.setValue(deleteDataReq.getData().get("sy_id"));
                        e.setType(ColumnType.STRING);
                        break;
                }
            });
        }

        // 请求中有null值需要额外处理
        if (FormCreateMode.EXIST_TABLE.equals(form.getCreateMode())) {
            List<SecurityColumnDto> securityDeleteColumns = new ArrayList<>();
            for (SecurityColumnDto securityColumn : securityColumns) {
                if (securityColumn.getValue() == null) {
                    FormComponentDto formComponentDto = componentMap.get(securityColumn.getName().split("\\.")[1]);
                    securityExecuteSql = securityExecuteSql.replace(formComponentDto.getFormValueCode() + " = ?",
                        formComponentDto.getFormValueCode() + " is null");
                } else {
                    securityDeleteColumns.add(securityColumn);
                }
            }

            // 执行安全sql
            datasource.securityExecuteSql(connectInfo, securityExecuteSql, securityDeleteColumns);
        } else {
            // 执行安全sql
            datasource.securityExecuteSql(connectInfo, securityExecuteSql, securityColumns);
        }
    }

    public void updateData(UpdateDataReq fomUpdateDataReq) {

        // 获取并校验表单
        FormEntity form = formService.getForm(fomUpdateDataReq.getFormId());

        // 校验表单是否发布
        if (!FormStatus.PUBLISHED.equals(form.getStatus())) {
            throw new IsxAppException("请先发布表单");
        }

        // 校验表单版本号是否一致
        if (!form.getFormVersion().equals(fomUpdateDataReq.getFormVersion())) {
            throw new IsxAppException("表单已重新发布，请刷新页面");
        }

        // 获取组件Map结构
        Map<String, FormComponentDto> componentMap = formService.getComponentMap(form.getId());

        // 数据源
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(form.getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());

        // 处理where中的条件
        List<SecurityColumnDto> securityColumnsForWhere = datasourceService.transSecurityColumns(form.getUpdateSql());
        String securityExecuteSql = datasourceService.transSecuritySql(form.getUpdateSql());
        formService.SetReqValueToSecurityColumns(componentMap, securityColumnsForWhere, fomUpdateDataReq.getOldData());

        // 获取set字段
        List<String> updateSetColumnList = new ArrayList<>();
        fomUpdateDataReq.getNewData().forEach((k, v) -> {
            FormComponentDto formComponent = componentMap.get(k);
            if (formComponent != null) {
                updateSetColumnList.add(formComponent.getFormValueCode() + " = '${" + k + "}'");
            }
        });

        // 补充系统字段
        if (FormCreateMode.CREATE_TABLE.equals(form.getCreateMode())
            || FormCreateMode.AUTO_TABLE.equals(form.getCreateMode())) {
            updateSetColumnList.add("last_modified_by = '${last_modified_by}'");
            updateSetColumnList.add("last_modified_date_time = '${last_modified_date_time}'");
        }

        // 生成完整的updateSql
        securityExecuteSql =
            securityExecuteSql.replace("'${UPDATE_COLUMN}'", StringUtils.join(updateSetColumnList, ","));

        // 处理set中的值
        List<SecurityColumnDto> securityColumnsForSet = datasourceService.transSecurityColumns(securityExecuteSql);
        securityExecuteSql = datasourceService.transSecuritySql(securityExecuteSql);
        formService.SetReqValueToSecurityColumns(componentMap, securityColumnsForSet, fomUpdateDataReq.getNewData());

        // 合并where和set
        securityColumnsForSet.addAll(securityColumnsForWhere);

        // 补充系统字段
        if (FormCreateMode.CREATE_TABLE.equals(form.getCreateMode())
            || FormCreateMode.AUTO_TABLE.equals(form.getCreateMode())) {
            securityColumnsForSet.forEach(e -> {
                String columnName = e.getName().substring(3);
                switch (columnName) {
                    case "sy_id":
                        e.setValue(fomUpdateDataReq.getOldData().get("sy_id"));
                        e.setType(ColumnType.STRING);
                        break;
                    case "last_modified_date_time":
                        e.setValue(new Date().getTime());
                        e.setType(ColumnType.TIMESTAMP);
                        break;
                    case "last_modified_by":
                        e.setValue(USER_ID.get());
                        e.setType(ColumnType.STRING);
                        break;
                }
            });
        }

        // 请求中有null值需要额外处理
        if (FormCreateMode.EXIST_TABLE.equals(form.getCreateMode())) {
            List<SecurityColumnDto> securityUpdateColumns = new ArrayList<>();
            for (SecurityColumnDto securityColumn : securityColumnsForSet) {
                if (securityColumn.getValue() == null
                    && securityColumnsForSet.indexOf(securityColumn) >= securityColumnsForSet.size() / 2) {
                    FormComponentDto formComponentDto = componentMap.get(securityColumn.getName().split("\\.")[1]);
                    String[] split = securityExecuteSql.split(" WHERE ");
                    securityExecuteSql =
                        split[0] + " WHERE " + split[1].replace(formComponentDto.getFormValueCode() + " = ?",
                            formComponentDto.getFormValueCode() + " is null");
                } else {
                    securityUpdateColumns.add(securityColumn);
                }
            }
            // 执行安全sql
            datasource.securityExecuteSql(connectInfo, securityExecuteSql, securityUpdateColumns);
        } else {
            // 执行安全sql
            datasource.securityExecuteSql(connectInfo, securityExecuteSql, securityColumnsForSet);
        }
    }

    public void deployForm(DeployFormReq deployFormReq) {

        FormEntity form = formService.getForm(deployFormReq.getFormId());

        // 在发布中，不可以直接发布
        if (FormStatus.PUBLISHED.equals(form.getStatus())) {
            throw new IsxAppException("请先下线表单");
        }

        // 重新创建一个表单版本号
        form.setStatus(FormStatus.PUBLISHED);
        form.setFormVersion(UUID.randomUUID().toString());
        formRepository.save(form);
    }

    public void offlineForm(OfflineFormReq offlineFormReq) {

        FormEntity form = formService.getForm(offlineFormReq.getFormId());

        form.setStatus(FormStatus.UNPUBLISHED);
        formRepository.save(form);
    }

    public void deleteForm(DeleteFormReq deleteFormReq) {

        FormEntity form = formService.getForm(deleteFormReq.getFormId());

        formRepository.deleteById(form.getId());

        // 删除组件
        formComponentRepository.deleteByFormId(form.getId());
    }

    /**
     * 表单数据查询.
     */
    public PageDataRes pageData(QueryDataReq fomQueryDataReq) {

        // 初始化返回对象
        PageDataRes pageDataRes = new PageDataRes();

        // 获取表单信息
        FormEntity form = formService.getForm(fomQueryDataReq.getFormId());

        // 如果查询sql为空，直接返回空
        if (Strings.isEmpty(form.getSelectSql())) {
            return pageDataRes;
        }

        // 获取数据源信息
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(form.getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());

        // 获取组件
        Map<String, FormComponentDto> componentMap = formService.getComponentMap(form.getId());

        // 获取查询sql
        String selectSql = form.getSelectSql();

        // 初始化返回数据对象
        List<Map<String, Object>> data = new ArrayList<>();

        // 添加搜索条件
        List<FormComponentDto> whereComponenList = new ArrayList<>();
        if (!Strings.isEmpty(fomQueryDataReq.getSearchKeyWord())) {
            List<String> likeSqlList = new ArrayList<>();
            componentMap.forEach((k, v) -> {
                if (FormComponentType.FORM_INPUT_NUMBER.equals(v.getComponentType())
                    || FormComponentType.FORM_INPUT_MONEY.equals(v.getComponentType())) {
                    try {
                        Double.parseDouble(fomQueryDataReq.getSearchKeyWord());
                    } catch (NumberFormatException e) {
                        return;
                    }
                    likeSqlList.add("`" + v.getFormValueCode() + "` = ? ");
                    whereComponenList.add(v);
                } else {
                    likeSqlList.add("`" + v.getFormValueCode() + "` like ? ");
                    whereComponenList.add(v);
                }
            });
            selectSql = selectSql + " where " + Strings.join(likeSqlList, " or ");
        }

        // 如果是自动创建模式，添加create_date_time排序功能
        if (FormCreateMode.CREATE_TABLE.equals(form.getCreateMode())
            || FormCreateMode.AUTO_TABLE.equals(form.getCreateMode())) {
            selectSql = selectSql + " order by create_date_time desc ";
        }

        // 生成预处理Sql
        String securityExecuteSql =
            datasource.getPageSql(selectSql).replace("'${pageSize}'", "?").replace("'${page}'", "?");

        // 执行sql
        try (Connection connection = datasource.getConnection(connectInfo);
            PreparedStatement statement = connection.prepareStatement(securityExecuteSql)) {

            // 搜索功能
            for (int i = 0; i < whereComponenList.size(); i++) {
                if (FormComponentType.FORM_INPUT_NUMBER.equals(whereComponenList.get(i).getComponentType())) {
                    statement.setDouble(i + 1, Double.parseDouble(fomQueryDataReq.getSearchKeyWord()));
                } else if (FormComponentType.FORM_INPUT_MONEY.equals(whereComponenList.get(i).getComponentType())) {
                    statement.setBigDecimal(i + 1,
                        BigDecimal.valueOf(Double.parseDouble(fomQueryDataReq.getSearchKeyWord())));
                } else {
                    statement.setString(i + 1, "%" + fomQueryDataReq.getSearchKeyWord() + "%");
                }
            }

            // 支持分页查询
            statement.setInt(whereComponenList.size() + (DatasourceType.MYSQL.equals(connectInfo.getDbType()) ? 1 : 2),
                fomQueryDataReq.getPage() * fomQueryDataReq.getPageSize());
            statement.setInt(whereComponenList.size() + (DatasourceType.MYSQL.equals(connectInfo.getDbType()) ? 2 : 1),
                fomQueryDataReq.getPageSize());

            // 获取结果
            ResultSet resultSet = statement.executeQuery();

            // 获取字段列数
            int columnCount = resultSet.getMetaData().getColumnCount();
            List<String> columnNameList = new ArrayList<>();
            for (int i = 1; i <= columnCount; i++) {
                columnNameList.add(resultSet.getMetaData().getColumnLabel(i));
            }

            // 解析翻译每条数据
            while (resultSet.next()) {
                Map<String, Object> metaData = new HashMap<>();
                for (int i = 0; i < columnNameList.size(); i++) {
                    formService.transComponentShowValue(componentMap.get(columnNameList.get(i)), columnNameList.get(i),
                        metaData, resultSet, i + 1);
                }
                // 封装返回数据
                data.add(metaData);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("查询失败");
        }

        // 查询总条数
        pageDataRes.setData(data);
        try (Connection connection = datasource.getConnection(connectInfo);
            Statement statement = connection.createStatement()) {
            String countQuery = "SELECT COUNT(*) FROM " + form.getMainTable();
            ResultSet resultSet = statement.executeQuery(countQuery);
            if (resultSet.next()) {
                pageDataRes.setCount(resultSet.getInt(1));
            }
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("总数查询失败");
        }
        return pageDataRes;
    }

    public GetFormLinkRes getFormLink(GetFormLinkReq getFormLinkReq) {

        // 校验分享表单是否存在
        FormEntity form = formService.getForm(getFormLinkReq.getFormId());

        // 校验表单是否发布
        if (!FormStatus.PUBLISHED.equals(form.getStatus())) {
            throw new IsxAppException("请先发布表单");
        }

        // 生成token
        String jwtToken = JwtUtils.encrypt(isxAppProperties.getAesSlat(), "sy_anonymous", isxAppProperties.getJwtKey(),
            getFormLinkReq.getValidDay() * 24 * 60);

        // 生成到期时间
        LocalDateTime invalidDateTime = LocalDateTime.now().plusDays(getFormLinkReq.getValidDay());

        // 封装entity
        FormLinkEntity formLink = FormLinkEntity.builder().formId(form.getId()).formVersion(form.getFormVersion())
            .invalidDateTime(invalidDateTime).formToken(jwtToken).build();

        // 持久化
        formLink = formLinkRepository.save(formLink);

        // 封装返回体
        return GetFormLinkRes.builder().formLinkId(formLink.getId()).build();
    }

    public GetFormLinkInfoRes getFormLinkInfo(GetFormLinkInfoReq getFormLinkInfoReq) {

        JPA_TENANT_MODE.set(false);

        Optional<FormLinkEntity> formLinkOptional = formLinkRepository.findById(getFormLinkInfoReq.getFormLinkId());

        if (!formLinkOptional.isPresent()) {
            throw new IsxAppException("链接不存在或已过期");
        }

        return formMapper.formLinkEntityToGetFormLinkInfoRes(formLinkOptional.get());
    }

    /**
     * 每天凌晨定时删除过期的链接.
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void scheduleDeleteFormLink() {

        JPA_TENANT_MODE.set(false);

        // 获取过期的链接
        List<FormLinkEntity> invalidFormLinkList =
            formLinkRepository.findAllByInvalidDateTimeBefore(LocalDateTime.now());

        // 一起删除
        formLinkRepository.deleteAll(invalidFormLinkList);
    }

}
