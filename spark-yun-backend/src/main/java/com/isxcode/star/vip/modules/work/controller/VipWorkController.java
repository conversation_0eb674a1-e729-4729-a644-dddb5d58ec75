package com.isxcode.star.vip.modules.work.controller;

import com.isxcode.star.api.main.constants.ModuleVipCode;
import com.isxcode.star.api.user.constants.RoleType;
import com.isxcode.star.api.workflow.req.DeployWorkflowReq;
import com.isxcode.star.api.workflow.req.KillWorkflowReq;
import com.isxcode.star.backend.api.base.constants.SecurityConstants;
import com.isxcode.star.common.annotations.successResponse.SuccessResponse;
import com.isxcode.star.vip.annotation.vip.VipApi;
import com.isxcode.star.vip.modules.work.service.VipWorkBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(name = "VIP作业模块")
@RequestMapping(ModuleVipCode.VIP_WORK)
@RestController
@RequiredArgsConstructor
public class VipWorkController {

    private final VipWorkBizService vipWorkBizService;

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "发布作业接口")
    @GetMapping("/deployWork")
    @SuccessResponse("发布成功")
    @Parameter(name = SecurityConstants.HEADER_TENANT_ID, description = "租户id", required = true,
        in = ParameterIn.HEADER, schema = @Schema(type = "string"))
    public void deployWork(
        @Schema(description = "作业唯一id", example = "sy_344c3d583fa344f7a2403b19c5a654dc") @RequestParam String workId) {

        vipWorkBizService.deployWork(workId);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "暂停作业接口")
    @GetMapping("/pauseWork")
    @SuccessResponse("暂停成功")
    @Parameter(name = SecurityConstants.HEADER_TENANT_ID, description = "租户id", required = true,
        in = ParameterIn.HEADER, schema = @Schema(type = "string"))
    public void pauseWork(
        @Schema(description = "作业唯一id", example = "sy_344c3d583fa344f7a2403b19c5a654dc") @RequestParam String workId) {

        vipWorkBizService.pauseWork(workId);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "启动作业接口")
    @GetMapping("/resumeWork")
    @SuccessResponse("启动成功")
    @Parameter(name = SecurityConstants.HEADER_TENANT_ID, description = "租户id", required = true,
        in = ParameterIn.HEADER, schema = @Schema(type = "string"))
    public void resumeWork(
        @Schema(description = "作业唯一id", example = "sy_344c3d583fa344f7a2403b19c5a654dc") @RequestParam String workId) {

        vipWorkBizService.resumeWork(workId);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "下线作业接口")
    @GetMapping("/deleteWork")
    @SuccessResponse("下线成功")
    @Parameter(name = SecurityConstants.HEADER_TENANT_ID, description = "租户id", required = true,
        in = ParameterIn.HEADER, schema = @Schema(type = "string"))
    public void killWork(
        @Schema(description = "作业唯一id", example = "sy_344c3d583fa344f7a2403b19c5a654dc") @RequestParam String workId) {

        vipWorkBizService.killWork(workId);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "发布作业流接口")
    @PostMapping("/deployWorkflow")
    @SuccessResponse("发布成功")
    @Parameter(name = SecurityConstants.HEADER_TENANT_ID, description = "租户id", required = true,
        in = ParameterIn.HEADER, schema = @Schema(type = "string"))
    public void deployWorkflow(@Valid @RequestBody DeployWorkflowReq deployWorkflowReq) {

        vipWorkBizService.deployWorkflow(deployWorkflowReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "下线作业流接口")
    @PostMapping("/killWorkflow")
    @SuccessResponse("下线成功")
    @Parameter(name = SecurityConstants.HEADER_TENANT_ID, description = "租户id", required = true,
        in = ParameterIn.HEADER, schema = @Schema(type = "string"))
    public void killWorkflow(@Valid @RequestBody KillWorkflowReq killWorkflowReq) {

        vipWorkBizService.killWorkflow(killWorkflowReq);
    }
}
