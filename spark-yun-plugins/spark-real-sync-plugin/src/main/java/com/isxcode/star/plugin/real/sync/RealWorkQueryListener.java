package com.isxcode.star.plugin.real.sync;

import com.isxcode.star.api.work.constants.WorkLog;
import org.apache.spark.sql.streaming.StreamingQueryListener;

import java.time.LocalDateTime;

public class RealWorkQueryListener extends StreamingQueryListener {

    private long totalNums = 0;

    @Override
    public void onQueryStarted(QueryStartedEvent event) {
        System.out.println("LogType:stdout-start");
    }

    @Override
    public void onQueryProgress(QueryProgressEvent event) {

        totalNums = totalNums + event.progress().numInputRows();
        System.out.println(LocalDateTime.now() + WorkLog.SUCCESS_INFO + " 累计处理条数: " + totalNums / 2);
    }

    @Override
    public void onQueryTerminated(QueryTerminatedEvent event) {
        // do nothing
    }
}
