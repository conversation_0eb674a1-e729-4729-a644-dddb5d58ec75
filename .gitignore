### Example user template template
### Example user template

# IntelliJ project files
.idea
*.iml
out
gen
### Gradle template
.gradle
**/build/
!src/**/build/

# Ignore Gradle GUI config
gradle-app.setting

# Avoid ignoring Gradle wrapper jar file (.jar files are usually ignored)
!gradle-wrapper.jar

# Avoid ignore Gradle wrappper properties
!gradle-wrapper.properties

# Cache of project
.gradletasknamecache

# Eclipse Gradle plugin generated files
# Eclipse Core
.project
# JDT-specific (Eclipse Java Development Tools)
.classpath

# 后端打包文件，不提交
**/build
# 后端打包文件，不提交
**/bin

# 工具包不提交，防止开发者把自己的token给提上去
spark-yun-isx/isx.sh

# .DS_Store不提交
**/.DS_Store
.DS_Store