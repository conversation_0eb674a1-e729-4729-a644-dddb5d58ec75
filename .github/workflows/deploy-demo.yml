name: deploy zhi<PERSON>yun

on:
  workflow_dispatch:
    inputs:
      admin_github_token:
        description: '管理员密钥'
        required: true
        type: string

env:
  ADMIN_GITHUB_TOKEN: ${{ inputs.admin_github_token }}

jobs:

  download:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write
      id-token: write

    steps:

      - name: Set timezone to Asia/Shanghai
        run: |
          sudo timedatectl set-timezone Asia/Shanghai
          date

      - name: Checkout spark-yun
        uses: actions/checkout@v4
        with:
          token: ${{ env.ADMIN_GITHUB_TOKEN }}
          repository: "isxcode/spark-yun"
          ref: 'main'

      - name: Download z<PERSON><PERSON><PERSON>
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          script_stop: true
          timeout: 1800s
          script: |
            rm -rf /tmp/zhiqingyun.tar.gz
            curl -ssL https://isxcode.oss-cn-shanghai.aliyuncs.com/zhiqingyun/zhiqingyun.tar.gz -o /tmp/zhiqingyun.tar.gz

  deploy:
    needs: download
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write
      id-token: write

    steps:

      - name: Set timezone to Asia/Shanghai
        run: |
          sudo timedatectl set-timezone Asia/Shanghai
          date

      - name: Deploy zhiqingyun
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          script_stop: true
          timeout: 1800s
          script: |
            tar -vzxf /tmp/zhiqingyun.tar.gz -C /opt/
            rm -rf /tmp/zhiliuyun.tar.gz
            bash /opt/zhiqingyun/bin/stop.sh
            if [ -d "/opt/zhiqingyun/resources_back" ]; then
              rm -rf /opt/zhiqingyun/resources
              mv /opt/zhiqingyun/resources_back /opt/zhiqingyun/resources
            fi
            rm -rf /opt/zhiqingyun/conf/application-local.yml
            cp /root/application-local.yml /opt/zhiqingyun/conf/application-local.yml
            cp -r /opt/zhiqingyun/resources /opt/zhiqingyun/resources_bak/resources_$(date +'%Y-%m-%d-%H-%M-%S')_bak
            bash /opt/zhiqingyun/bin/start.sh --print-log=false
            sleep 120
            until curl -s https://zhiqingyun-demo.isxcode.com/tools/open/health | grep "UP"; do
              echo "Waiting for service to be available..."
              sleep 1
            done