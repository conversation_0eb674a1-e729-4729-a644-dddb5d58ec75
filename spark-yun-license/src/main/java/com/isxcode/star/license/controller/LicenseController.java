package com.isxcode.star.license.controller;

import com.isxcode.star.api.license.req.QueryLicenseReq;
import com.isxcode.star.api.license.res.QueryLicenseRes;
import com.isxcode.star.api.main.constants.ModuleVipCode;
import com.isxcode.star.api.user.constants.RoleType;
import com.isxcode.star.common.annotations.successResponse.SuccessResponse;
import com.isxcode.star.license.service.LicenseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "许可证模块")
@RequestMapping(ModuleVipCode.VIP_LICENSE)
@RestController
@RequiredArgsConstructor
public class LicenseController {

    private final LicenseService licenseService;

    @Secured({RoleType.SYS_ADMIN})
    @PostMapping("/uploadLicense")
    @Operation(summary = "证书上传接口(Swagger有Bug不能使用)")
    @SuccessResponse("上传成功")
    public void uploadLicense(@RequestParam("license") MultipartFile license) {

        licenseService.uploadLicense(license);
    }

    @Secured({RoleType.SYS_ADMIN})
    @GetMapping("/enableLicense")
    @Operation(summary = "启用证书接口")
    @SuccessResponse("启用成功")
    public void enableLicense(@Schema(description = "许可证唯一id", example = "sy_344c3d583fa344f7a2403b19c5a654dc")
    @RequestParam String licenseId) {

        licenseService.enableLicense(licenseId);
    }

    @Secured({RoleType.SYS_ADMIN})
    @GetMapping("/disableLicense")
    @Operation(summary = "禁用证书接口")
    @SuccessResponse("禁用成功")
    public void disableLicense(@Schema(description = "许可证唯一id", example = "sy_344c3d583fa344f7a2403b19c5a654dc")
    @RequestParam String licenseId) {

        licenseService.disableLicense(licenseId);
    }

    @Secured({RoleType.SYS_ADMIN})
    @GetMapping("/deleteLicense")
    @Operation(summary = "删除证书接口")
    @SuccessResponse("删除成功")
    public void deleteLicense(@Schema(description = "许可证唯一id", example = "sy_344c3d583fa344f7a2403b19c5a654dc")
    @RequestParam String licenseId) {

        licenseService.deleteLicense(licenseId);
    }

    @Secured({RoleType.SYS_ADMIN})
    @PostMapping("/queryLicense")
    @Operation(summary = "查询证书列表接口")
    @SuccessResponse("查询成功")
    public Page<QueryLicenseRes> queryLicense(@Valid @RequestBody QueryLicenseReq licQueryLicenseReq) {

        return licenseService.queryLicense(licQueryLicenseReq);
    }
}
