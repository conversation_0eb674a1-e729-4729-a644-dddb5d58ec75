{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "noEmit": true, "allowImportingTsExtensions": true, "sourceMap": true, "allowJs": true, "baseUrl": ".", "types": ["vite/client"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "src/views/computer-group/components/utils.js"], "exclude": ["node_modules"], "files": [], "references": [{"path": "./tsconfig.node.json"}, {"path": "./tsconfig.app.json"}, {"path": "./tsconfig.vitest.json"}]}