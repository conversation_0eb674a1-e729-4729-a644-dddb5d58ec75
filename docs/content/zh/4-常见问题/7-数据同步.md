---
title: "数据同步"
---

#### 问题1: 数据同步动态分区报错

**解决方案**

> 点击作业`配置`,在资源配置中选择`高级定义`，并添加自定义的json参数配置
> "hive.exec.dynamic.partition.mode": "nonstrict" 

![20240824181823](https://img.isxcode.com/picgo/20240824181823.png)

#### 问题2: 数据同步添加静态值报错

**解决方案**

```wikitext
注意这里要使用单引号
```

![20240824181909](https://img.isxcode.com/picgo/20240824181909.png)

#### 问题3: String or binary data would be truncated.

> SqlServer执行异常

```log
String or binary data would be truncated.
```

**解决方案**

```wikitext
检查数据库中字段的长度是否受限制，比如varchar(10)太短，需要改成varchar(2000)
```

```sql
alter table users
    alter column username varchar(2000) null
go
```