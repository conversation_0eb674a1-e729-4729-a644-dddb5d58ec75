---
title: "计算集群"
---

#### 问题1: Standalone模式，默认安装spark服务，无法激活节点

查看至轻云服务日志

```bash
cd /opt/zhiqingyun/logs
tail -f spark-yun.log
```

查看至轻云节点日志

```bash
cd ~/zhiqingyun-agent/logs
tail -f zhiqingyun-agent.log
```

#### 问题2: 作业提交状态一直卡住SUBMITTED

访问`http://localhost:8081`

> 检查spark服务是否有workers节点激活，正常如下图

![20241206175108](https://img.isxcode.com/picgo/20241206175108.png)

> 检查worker节点启动日志

```bash
cd ~/zhiqingyun-agent/spark-min/logs
cat spark-ispong-org.apache.spark.deploy.worker.Worker-1-ispong-mac.local.out
```

> 检查~/.bashrc中是否配置JAVA_HOME变量

```bash
tee -a ~/.bashrc <<-'EOF'
export JAVA_HOME=/opt/java
export PATH=$PATH:$JAVA_HOME/bin
EOF
source ~/.bashrc
java -version
```

或者手动启动worker

```bash
cd ~/zhiqingyun-agent/spark-min/sbin
bash start-worker.sh --webui-port 8081 spark://localhost:7077
```

#### 问题3: Spark的Master和Worker服务都启动正常，但是无法提交作业7077无法访问

```wikitext
检查`~/zhiqingyun-agent/spark-min/conf`下的`spark-defaults.conf`文件中配置是否正确
```

![20241212171427](https://img.isxcode.com/picgo/20241212171427.png)

**解决方案**

```bash
# 需要和`http://localhost:8081`的Spark URL保持一致
spark.master          spark://localhost:7077
# 需要和`http://localhost:8081`的访问地址保持一直 
spark.master.web.url  http://localhost:8081
```

#### 问题4: 独立启动spark服务

```bash
cd ~/zhiqingyun-agent/spark-min/sbin
bash start-master.sh --webui-port 30170 --host localhost --port 30166
bash start-worker.sh --webui-port 30168 --host localhost --port 30169 -c 12 -m 20g spark://localhost:30166 

# 停止服务
bash stop-master.sh
bash stop-worker.sh
```

> 修改spark配置

```bash
vim ~/zhiqingyun-agent/spark-min/conf/spark-defaults.conf
```

```bash
spark.master          spark://localhost:30166
spark.master.web.url  http://localhost:30170
```

Spark Master启动命令说明

```bash
Options:
-i HOST, --ip HOST     Hostname to listen on (deprecated, please use --host or -h) 
-h HOST, --host HOST   Hostname to listen on
-p PORT, --port PORT   Port to listen on (default: 7077)
--webui-port PORT      Port for web UI (default: 8080)
--properties-file FILE Path to a custom Spark properties file.
                       Default is conf/spark-defaults.conf.
```

Spark Worker启动命令说明

```bash
Options:
-c CORES, --cores CORES  Number of cores to use
-m MEM, --memory MEM     Amount of memory to use (e.g. 1000M, 2G)
-d DIR, --work-dir DIR   Directory to run apps in (default: SPARK_HOME/work)
-i HOST, --ip IP         Hostname to listen on (deprecated, please use --host or -h)
-h HOST, --host HOST     Hostname to listen on
-p PORT, --port PORT     Port to listen on (default: random)
--webui-port PORT        Port for web UI (default: 8081)
--properties-file FILE   Path to a custom Spark properties file.
                         Default is conf/spark-defaults.conf. 
```

#### 问题5: 作业提交卡死，Spark Standalone资源在排队，但是实际系统资源充足

```log
standalone中的内存和核心都是虚拟的,可以通过修改配置扩大内存和核心数。
```

**解决方案**

```bash
vim ~/zhiqingyun-agent/spark-min/conf/spark-env.sh
```

```bash
# 最多可配置当前内存数*2，比如16GB内存，可配置16*2个虚拟核心
EXPORT SPARK_WORKER_CORES=32 
# 最多可配置当前的内存数*2，比如16GB的内存，可配置16*2个虚拟内存
EXPORT SPARK_WORKER_MEMORY=32g 
```

```wikitext
通过界面停止节点，并重新激活节点，让配置生效
```

#### 问题6: 节点安装日志报错

```log
stop.sh:行2: $'\r'：未找到命令
stop.sh: 第 5 行：cd: $'/tmp/zhiqingyun/bin\r': 没有那个文件或目录
stop.sh:行5: $'exit\r'：未找到命令
stop.sh:行7: $'\r'：未找到命令
stop.sh:行21: 语法错误: 未预期的文件结尾
```

**解决方案**

```wikitext
使用windows打的包，无法直接部署到linux系统，需要对bash脚本中的/r做兼容
```

```bash
cd ~/zhiqingyun-agent/bin
sed -i 's/\r//g' start.sh
# 可手动启动节点
bash start.sh
# 也可通过界面激活
```

#### 问题7: io读写数据异常

```wikitext
首页监控基于sysstat工具实现
```

> 安装命令

```bash
sudo apt install -y sysstat
# or
sudo yum install -y sysstat
```