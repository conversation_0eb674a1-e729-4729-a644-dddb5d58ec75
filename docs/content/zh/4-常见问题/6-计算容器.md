---
title: "计算容器"
---

#### 问题1: 计算容器启动一直处于启动状态，特别是k8s集群

> 排查节点日志

```bash
cd ~/zhiqingyun-agent/works
cat ${containerId}.log
```

#### 问题2: k8s集群启动的容器，查询报错500或者一直loading

> 进入k8s集群，查询节点日志

```bash
kubectl get pods -n zhiqingyun-space
kubectl logs -f zhiqingyun-spark-container-sql-${containerId}-${containerId}-${podId}-driver -n zhiqingyun-space 
```

```log
一般由于域名无法访问导致的
计算容器，资源类型选择自定义，添加域名映射关系
```

```json
{
  "qing.host1.name": "zhiqingyun-host1",
  "qing.host1.value": "127.0.0.1",
  "qing.host2.name": "zhiqingyun-host2",
  "qing.host2.value": "127.0.0.1",
  "qing.host3.name": "zhiqingyun-host3",
  "qing.host3.value": "127.0.0.1"
}
```