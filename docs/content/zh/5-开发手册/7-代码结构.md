---
title: "代码结构"
---

## 项目代码结构说明

```wikitext
├── .checkstyle                           #【架构】检测java代码checkstyle规则配置中心 
├── .github                               #【所有人】github相关配置
│   └── ISSUE_TEMPLATE                    #【产品】github中issue模版配置
├── .idea                                 #【所有人】idea开发环境初始化
├── .vscode                               #【所有人】vscode开发环境初始化
├── gradle                                #【架构】gradle项目构建工具
├── docs                                  #【产品】官网文档
├── spark-yun-agent                       #【后端】至轻云代理代码
├── spark-yun-backend                     #【后端】至轻云后端代码 
│   ├── spark-yun-api                     #【后端】至轻云的所有对象层    
│   ├── spark-yun-api-base                #【架构】架构框架
│   ├── spark-yun-common                  #【架构】架构框架
│   ├── spark-yun-main                    #【架构】架构框架
│   ├── spark-yun-modules                 #【后端】java的服务层，包含所有的业务模块
│   ├── spark-yun-security                #【架构】安全模块
├── spark-yun-client                      #【后端】至轻云客户端代码 
├── spark-yun-dist                        #【架构】项目打包模块
│   ├── zhiqingyun                        #【架构】打包至轻云模块
│   ├── zhiqingyun-agent                  #【架构】打包至轻云代理模块
├── spark-yun-frontend                    #【前端】至轻云前端代码
├── spark-yun-plugins                     #【后端】至轻云插件中心
├── spark-yun-vip                         #【所有人】不开源的代码！！！
│   ├── spark-yun-autotest                #【测试】自动化测试代码
│   ├── spark-yun-backend                 #【后端】后端收费代码
│   ├── spark-yun-license                 #【架构】许可证模块代码
│   ├── spark-yun-plugins                 #【后端】收费插件
├── .editorconfig                         #【所有人】editorconfig代码格式配置
├── .gitignore                            #【所有人】git忽略提交配置
├── .gitpod.yml                           #【所有人】支持gitpod远程开发
├── .mailmap                              #【架构】开发者联系方式
├── CODE_OF_CONDUCT.md                    #【架构】github规范
├── CONTRIBUTING.md                       #【架构】github规范
├── Dockerfile                            #【架构】至轻云打包镜像脚本
├── LICENSE                               #【架构】许可证
├── README.md                             #【产品】github官网
├── VERSION                               #【架构】当前版本
├── build.gradle                          #【所有人】gradle构建脚本
├── gradle.properties                     #【所有人】gradle环境变量
├── gradlew                               #【架构】gradle的bash启动脚本
├── gradlew.bat                           #【架构】gradle的bat启动脚本
└── settings.gradle                       #【所有人】gradle设置
```

开源仓库：https://github.com/isxcode/spark-yun  
闭源仓库：https://github.com/isxcode/spark-yun-vip 
> 两部分代码组合在一起才是完整的至轻云