---
title: "作业流"
---

#### 查询作业流

> 点击`作业流`菜单，查看租户中的作业流列表
> 支持作业流名称、备注搜索

![20241219172017](https://img.isxcode.com/picgo/20241219172017.png)

> 作业流支持`发布`、`下线`、`删除`、`编辑`操作

- **发布**:作业流可配置调度时间，发布操作，可实现作业流的定时调度，同时刷新下次执行时间
- **下线**:将调度中的作业流，停止调度 
- **删除**:删除作业流，包括作业流中的作业和作业配置 
- **编辑**:编辑作业流的名称和备注

#### 新建作业流

> 点击`添加作业流`按钮，添加新的作业流
> 注意！！！作业流的数量受限于`许可证的作业流数`和当前`租户内配置的作业流数`

![20241220145832](https://img.isxcode.com/picgo/20241220145832.png)

- **名称**:必填，作业流的名称，租户内名称唯一
- **备注**:非必填

#### 查询作业列表

> 点击`作业流名称`，可进入作业流的编辑页面

![20240518103336](https://img.isxcode.com/picgo/20240518103336.png)

> 工具栏支持`运行`、`中止`、`重跑`、`保存`、`配置`、`发布`、`下线`、`拖拽`操作

- **运行**:运行作业流画布中的作业，按照拖拽的逻辑运行 
- **中止**:将运行中的作业流中止 
- **重跑**:重跑作业流中的作业 
- **保存**:保存作业流中的作业和画布配置 
- **配置**:可配置作业流的`调度`、`基线告警`、`外部调用`
- **发布**:发布操作，可实现作业流的定时调度，同时刷新下次执行时间 
- **下线**:将调度中的作业流，停止调度 
- **拖拽**:可自由的将左边列表中的作业，任意拖拽到右侧的画布中

#### 作业流快速切换

> 通过作业流名称右侧的切换按钮，可快速切换开发的作业流

![20241220152702](https://img.isxcode.com/picgo/20241220152702.png)

#### 配置作业流

> 点击工具栏中的`配置`按钮，可对作业流进行配置

![20241220150258](https://img.isxcode.com/picgo/20241220150258.png)

#### 配置项说明

###### 调度配置

> 配置作业流的调度时间
> 支持两种模式: 单一调度、离散调度

- **单一调度**:仅需要对作业流进行调度时间配置，作业的调度时间以作业流配置的时间为准 
- **离散调度**:需要对作业流和作业进行单独的调度配置，作业的运行以各自配置的调度时间为准
> 注意！！！离散调度模式，推荐作业流的调度时间小于作业的配置时间，先运行作业流，然后运行子作业
> 注意！！！修改调度时间后，一定要通过先下线再发布的方式，让调度配置生效

- **高级定义**:支持用户手动填写cron表达式配置调度时间，参考地址:https://cron.qqe2.com/  

![20241220151404](https://img.isxcode.com/picgo/20241220151404.png)

###### 基线告警

> 对作业流调度过程中的事件进行通知
> 支持事件包括:作业流开始运行、作业流结束运行、作业流运行异常、作业流运行成功

###### 外部调用

> 支持作业流通过接口的方式被第三方触发，类似以下curl调用内容
> 注意！！！作业流需要重新发布，外部调用才会生效，token当前有效期时间为`1年`

```bash
curl -s 'http://zhiqingyun-demo.isxcode.com/workflow/open/invokeWorkflow' \
   -H 'Content-Type: application/json;charset=UTF-8' \
   -H 'Accept: application/json, text/plain, */*' \
   --data-raw '{"workflowId":"sy_1854732390999683072","token":"eyJhbGciOiJIUzI1NiJ9.eyJDTEFJTSI6IjFyWUxkVitHTlo0cGZINVNReURqQXI3b2QyM3A3TTNOUmpjVmcwZ3RJSXVtMmhYaElESGdjbEpGQ2hJOHJyOTg1Tnl6bk90OVgwWnFsWGY5MUp5ZUJucHE0VXd6NDEvTDNENlZlYW5aRitOdGxidkZMRUdNL0Q3N1prdHZ3dVdCV2twd0pMWS8rQXFzNS9YbU5rakNIMlRObmN6VmY0VG5jc3MrUWtlekk5WmtuRTlpUEthZGRPTDEyY0haTFVEOSIsImlhdCI6MTczNDY3ODE2NSwianRpIjoiNjRiY2QwNWMtNzFiNi00YjIwLWJjMTctOWNmNzRjMzE5NzAyIiwiZXhwIjoxNzY2MjE0MTY1fQ.VFw_L-sNOgg6_OkMR2dFbAkGUZ7-IEmalAB5MM-HXAI"}'
```