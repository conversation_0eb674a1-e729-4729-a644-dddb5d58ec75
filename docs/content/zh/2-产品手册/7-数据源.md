---
title: "数据源"
---

#### 查看数据源

> 点击`数据源`菜单，查看当前租户创建的数据源
> 支持名称、类型、连接信息、用户名、备注搜索，支持下拉`数据源类型`搜索

![20241213185108](https://img.isxcode.com/picgo/20241213185108.png)

#### 添加数据源

点击`添加数据源`按钮

![20241213184832](https://img.isxcode.com/picgo/20241213184832.png)

**支持以下数据源类型**

> 注意！！！连接信息中一定要携带数据库的db名称或者schema名称，优先推荐使用内网ip连接

| 数据源        | 连接信息模版                                                      |
|------------|-------------------------------------------------------------|
| Mysql      | ***********************************                         |
| Doris      | ***********************************                         |
| SqlServer  | *********************************************************** | 
| Oracle     | *******************************************                 |
| Hive       | ************************************                        |
| Postgres   | ****************************************                    |
| Clickhouse | ****************************************                    |
| Hana       | **********************************                          |
| 达梦         | jdbc:dm://127.0.0.1:5236/test_db                            |
| OceanBase  | ***************************************                     | 
| Tidb       | ***********************************                         | 
| StarRocks  | ***********************************                         | 
| Kafka      | 127.0.0.1:9092                                              | 

#### 连接信息说明

- **名称**:必填，数据源名称，租户内唯一 
- **类型**:必填，下拉数据源类型选择 
- **数据源驱动**:必填，默认使用系统提供的驱动，可选择`驱动管理`中的驱动 
- **连接信息**:必填，数据库连接信息，连接信息参考模版，注意结尾带上db库名或者schema名称  
优先推荐使用内网ip连接
- **用户**:非必填，数据源账户 
- **密码**:非必填，数据源密码 
- **备注**:非必填 
- **hive.metastore.uris**: 只有hive数据源有该选项，推荐使用内网ip，默认端口号为`9083`
- **topic**: 只有kafka数据源有该选项，推荐使用内网ip，默认端口号为`9092`

#### 数据源检测

> `检测`数据源，排查数据源的连接问题
> 举例：以下报错内容为密码错误

![20241213184910](https://img.isxcode.com/picgo/20241213184910.png)

![20241213184945](https://img.isxcode.com/picgo/20241213184945.png)

![20241213184957](https://img.isxcode.com/picgo/20241213184957.png)

