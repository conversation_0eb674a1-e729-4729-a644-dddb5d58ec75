---
title: "基线告警"
---

#### 基线告警

> 用于作业流调度中，指定事件，对指定人的消息发送服务
> 发送方式：阿里短信、邮箱
> 事件包括：作业/作业流运行前、作业/作业流运行后、作业/作业流运行成功、作业/作业流运行失败

#### 查看消息体

> 基线告警 > 消息体
> 支持阿里短信和邮箱消息体

![20241219174026](https://img.isxcode.com/picgo/20241219174026.png)

#### 新建阿里短信消息体

> 选择`阿里短信`类型

![20241219175029](https://img.isxcode.com/picgo/20241219175029.png)

###### 阿里短信配置参考文档：

> https://next.api.aliyun.com/api/Dysmsapi/2017-05-25/SendSms?RegionId=cn-hangzhou-finance

- **服务地址(region)**: 必填，选择短信服务所在的服务器地址 
- **AccessKeyId**: 必填，阿里云账号AccessKeyId 
- **AccessKeySecret**: 必填，阿里云账号AccessKeySecret 
- **短信签名名称(SignName)**: 必填，短信签名 
- **短信模板Code(TemplateCode)**: 必填，阿里云短信的模版Code 
- **短信模版样例(TemplateParam)**: 必填，阿里云模版短信的请求参数
- **备注**: 非必填

> 阿里云模版短信的请求参数，根据不同的模版有不同的json请求体

```json
{
  "code": "123456"
}
```

#### 新建邮箱消息体

> 选择`邮箱`类型

![20241219175046](https://img.isxcode.com/picgo/20241219175046.png)

- **服务地址(Host)**: 必填，邮箱服务器的host地址 
- **端口号(Port)**: 必填，邮箱服务的端口号，qq邮箱默认`587` 
- **账号(Username)**: 必填，邮箱服务器的账号 
- **密码**: 必填，邮箱服务器的账号密码 
- **主题(Subject)**: 必填，邮件的主题 
- **邮箱模版样例**: 必填，邮件内容模版 
- **备注**: 非必填

#### 检测消息体

> 点击`检测`按钮
> 检测消息体是否可以正常发送短信
> 需要指定发送对象和通知内容

![20241219175111](https://img.isxcode.com/picgo/20241219175111.png)

> 如果正常发送通知，会变成`检测成功`状态

![20241220181031](https://img.isxcode.com/picgo/20241220181031.png)

#### 启动消息体

> `检测成功`状态的消息体还不能正常使用，需要启动消息体

![20241220181154](https://img.isxcode.com/picgo/20241220181154.png)

#### 基线配置

> 基线告警 > 基线配置
> 指定事件，指定发送对象，指定发送内容

![20241219175206](https://img.isxcode.com/picgo/20241219175206.png)

![20241220181315](https://img.isxcode.com/picgo/20241220181315.png)

- **名称**: 必填，基线的名称，租户内名称唯一 
- **类型**: 必填，包括`作业`，`作业流` 
- **告警事件**: 必填，包括`开始运行`、`运行结束`、`运行成功`、`运行失败`
- **消息通知**: 必填，选择`消息体`中，已启用的消息体 
- **通知人**: 必填，多选租户内的成员 
- **通知内容**: 必填，发送消息的内容 
- **备注**: 非必填

##### 通知内容说明

> 支持函数模版

| 参数                           | 参数说明                      |
|------------------------------|---------------------------|
| ${qing.work_name}            | 作业名称                      |
| ${qing.work_id}              | 作业id                      |
| ${qing.workflow_name}        | 作业流名称                     |
| ${qing.workflow_id}          | 作业流id                     |
| ${qing.work_instance_id}     | 作业实例id                    |
| ${qing.workflow_instance_id} | 作业流实例id                   |
| ${qing.current_datetime}     | 当前时间(yyyy-MM-dd HH:mm:ss) |
| ${qing.current_date}         | 当前时间(yyyy-MM-dd)          |

##### 参考模版

```wikitext
您好，您创建的【${qing.workflow_name}】作业流下的【${qing.work_name}】作业，在【${qing.current_datetime}】时间运行失败了，
请及时处理。作业流实例id为【${qing.workflow_id}】，运行失败的作业实例id为【${qing.work_instance_id}】。
```

![20241220182935](https://img.isxcode.com/picgo/20241220182935.png)

#### 告警实例

> 记录发送通知的具体详情，包括`通知状态`、`响应内容`、`通知内容`等

![20241219175329](https://img.isxcode.com/picgo/20241219175329.png)

> 阿里短信发送的响应信息

![20241219175344](https://img.isxcode.com/picgo/20241219175344.png)

> 阿里短信的发送内容

![20241219175357](https://img.isxcode.com/picgo/20241219175357.png)

> 邮箱的发送内容

![20241220182850](https://img.isxcode.com/picgo/20241220182850.png)