---
title: "驱动上传"
---

#### 查看数据源驱动

> 管理租户中的数据源驱动
> 对于同一类型的数据库可设置默认数据源驱动，默认的数据源驱动将优先默认选中

![20241219170937](https://img.isxcode.com/picgo/20241219170937.png)

![20241220143342](https://img.isxcode.com/picgo/20241220143342.png)

#### 添加数据源驱动

> 选择数据源驱动对应数据源的类型，拖拽对应的驱动jar包上传即可

![20240518114539](https://img.isxcode.com/picgo/20240518114539.png)

- **驱动名称**:必填，租户内名称唯一 
- **类型**: 必填，数据源的类型，如Mysql、Oracle等 
- **驱动**: 必填，数据源驱动的jar包 
- **备注**: 非必填


