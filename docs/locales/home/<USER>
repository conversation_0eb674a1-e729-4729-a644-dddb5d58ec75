{"home": "Home", "zhi_qing_yun": "Zhiqing Cloud", "enterprise_data_platform": "Lightweight Big Data Computing Platform", "quick_start": "Quick Start", "experience_now": "Experience Now", "video_introduction": "Video Introduction", "copy_success": "Copy Successful", "choose_light_cloud": "Choose Zhiqing Cloud", "light_cloud_description": "Zhiqing Cloud is an ultra-lightweight, enterprise-grade big data computing platform. With one-click deployment and ready-to-use features, it enables scenarios such as big data ETL, real-time computing, visual scheduling, custom interfaces, data dashboards, and shareable forms. It helps enterprises process massive data and unlock more business value.", "light_cloud_description_mobile": "Zhiqing Cloud is an ultra-lightweight, enterprise-grade big data platform featuring one-click deployment and ready-to-use functionality. Without requiring additional big data components, it quickly enables enterprise-level big data offline ETL, real-time computing, and job scheduling.", "related_technologies": "Related Technologies", "coding_capability": "Can Code? It's More Than Just SQL", "job_types_supported": "Supports a wide range of job types, including SparkSQL, SparkJar, data synchronization, JdbcSQL, Prql, Excel import, Python scripts, Bash scripts, Curl scripts, API calls, and more.", "job_orchestration": "<PERSON>, Drag-and-Drop Any Way You Like", "job_support": "Fully supports job lifecycle commands, including run, abort, interrupt, offline, publish, rerun, rerun downstream, rerun current, and external calls.", "real_work": "Real-Time Computing for Higher Efficiency", "real_work_description": "Supports real-time computing and real-time database synchronization using Kafka and Debezium for efficient data stream processing, ensuring rapid data transfer and updates.", "data_drives_value": "Data Creates Value, Big Data Creates Big Value", "data_drives_value_description": "AI is here. Zhiqing Cloud partners with you to implement an enterprise-grade big data platform.", "multi_platform_deployment": "Multi-Platform Deployment, Always Adaptable", "multi_platform_description": "Supports various resource scheduling platforms such as Kubernetes, Yarn, CDH, Apache Hadoop, Spark Standalone, Spark Cluster, and more.", "data_view": "Data Dashboards, Show Big Data", "opensource_value": "Open Source Empowers Enterprises to Utilize Big Data Capabilities", "data_view_description": "Powerful data processing and visualization technology transform complex data into intuitive charts, supporting real-time monitoring and analysis to aid enterprise decision-making and adapt to industry needs.", "free_trial": "Free Trial", "zhi_yao_shu_ju": "Zhiyao Data", "build_enterprise_open_source_software": "Lightweight Big Data Computing Platform"}